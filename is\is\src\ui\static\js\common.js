/**
 * 通用JavaScript功能模块
 * 包含全局工具函数和通用组件
 */

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api',
    WEBSOCKET_URL: 'ws://localhost:5000/ws',
    MESSAGE_TIMEOUT: 5000,
    PROGRESS_UPDATE_INTERVAL: 1000
};

// 工具函数类
class Utils {
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} timeout - 自动消失时间（毫秒）
     */
    static showMessage(message, type = 'info', timeout = CONFIG.MESSAGE_TIMEOUT) {
        const messageContainer = document.getElementById('messageContainer');
        if (!messageContainer) return;

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show message-toast`;
        alertDiv.innerHTML = `
            <i class="fas fa-${this.getIconByType(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        messageContainer.appendChild(alertDiv);

        // 自动消失
        if (timeout > 0) {
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, timeout);
        }
    }

    /**
     * 根据消息类型获取图标
     * @param {string} type - 消息类型
     * @returns {string} 图标类名
     */
    static getIconByType(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * 显示确认对话框
     * @param {string} title - 对话框标题
     * @param {string} message - 确认消息
     * @param {Function} onConfirm - 确认回调函数
     */
    static showConfirm(title, message, onConfirm) {
        const modal = document.getElementById('confirmModal');
        const titleElement = document.getElementById('confirmModalTitle');
        const bodyElement = document.getElementById('confirmModalBody');
        const confirmButton = document.getElementById('confirmModalConfirm');

        if (!modal || !titleElement || !bodyElement || !confirmButton) return;

        titleElement.textContent = title;
        bodyElement.textContent = message;

        // 移除之前的事件监听器
        const newConfirmButton = confirmButton.cloneNode(true);
        confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

        // 添加新的事件监听器
        newConfirmButton.addEventListener('click', () => {
            onConfirm();
            bootstrap.Modal.getInstance(modal).hide();
        });

        // 显示模态框
        new bootstrap.Modal(modal).show();
    }

    /**
     * 格式化数字
     * @param {number} num - 数字
     * @returns {string} 格式化后的字符串
     */
    static formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * 格式化时间
     * @param {Date|string} date - 日期对象或字符串
     * @returns {string} 格式化后的时间字符串
     */
    static formatTime(date) {
        if (!date) return '--';
        
        const d = new Date(date);
        const now = new Date();
        const diff = now - d;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 1天内
            return Math.floor(diff / 3600000) + '小时前';
        } else {
            return d.toLocaleDateString('zh-CN');
        }
    }

    /**
     * 验证Instagram URI格式
     * @param {string} uri - URI字符串
     * @returns {boolean} 是否有效
     */
    static validateInstagramURI(uri) {
        const patterns = [
            /^https?:\/\/(www\.)?instagram\.com\/p\/[A-Za-z0-9_-]+\/?.*$/,
            /^https?:\/\/(www\.)?instagram\.com\/reel\/[A-Za-z0-9_-]+\/?.*$/
        ];
        return patterns.some(pattern => pattern.test(uri.trim()));
    }

    /**
     * 从URI中提取媒体ID
     * @param {string} uri - Instagram URI
     * @returns {string|null} 媒体ID
     */
    static extractMediaId(uri) {
        const match = uri.match(/\/(?:p|reel)\/([A-Za-z0-9_-]+)/);
        return match ? match[1] : null;
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深拷贝对象
     * @param {Object} obj - 要拷贝的对象
     * @returns {Object} 拷贝后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    /**
     * 生成UUID
     * @returns {string} UUID字符串
     */
    static generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
}

// API请求类
class ApiClient {
    /**
     * 发送GET请求
     * @param {string} endpoint - API端点
     * @param {Object} params - 查询参数
     * @returns {Promise} 请求Promise
     */
    static async get(endpoint, params = {}) {
        const url = new URL(CONFIG.API_BASE_URL + endpoint, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw new Error(`网络请求失败: ${error.message}`);
        }
    }

    /**
     * 发送POST请求
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 请求Promise
     */
    static async post(endpoint, data = {}) {
        try {
            const response = await fetch(CONFIG.API_BASE_URL + endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw new Error(`网络请求失败: ${error.message}`);
        }
    }

    /**
     * 处理响应
     * @param {Response} response - Fetch响应对象
     * @returns {Promise} 处理后的数据
     */
    static async handleResponse(response) {
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    }
}

// 本地存储管理类
class StorageManager {
    /**
     * 设置本地存储项
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    static set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('存储数据失败:', error);
        }
    }

    /**
     * 获取本地存储项
     * @param {string} key - 键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 存储的值
     */
    static get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    }

    /**
     * 删除本地存储项
     * @param {string} key - 键名
     */
    static remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('删除数据失败:', error);
        }
    }

    /**
     * 清空所有本地存储
     */
    static clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空数据失败:', error);
        }
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        Utils.showMessage('发生了一个错误，请刷新页面重试', 'error');
    });

    // 全局未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
        Utils.showMessage('操作失败，请重试', 'error');
    });
});

// 导出到全局作用域
window.Utils = Utils;
window.ApiClient = ApiClient;
window.StorageManager = StorageManager;
