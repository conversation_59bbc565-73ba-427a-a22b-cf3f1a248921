"""
常量定义模块
定义系统中使用的所有常量
"""

# 应用常量
APP_NAME = "Instagram Comment Extractor"
APP_VERSION = "1.0.0"

# 数据库常量
DB_TABLE_USERS = "users"
DB_BATCH_SIZE = 100

# Instagram API常量
INSTAGRAM_BASE_URL = "https://www.instagram.com"
INSTAGRAM_API_DELAY_MIN = 1
INSTAGRAM_API_DELAY_MAX = 3
INSTAGRAM_MAX_RETRIES = 3
INSTAGRAM_TIMEOUT = 30

# 导出格式常量
EXPORT_FORMAT_JSON = "json"
EXPORT_FORMAT_CSV = "csv"
EXPORT_FORMAT_EXCEL = "excel"

SUPPORTED_EXPORT_FORMATS = [
    EXPORT_FORMAT_JSON,
    EXPORT_FORMAT_CSV,
    EXPORT_FORMAT_EXCEL
]

# 用户数据字段常量
USER_FIELDS_REQUIRED = [
    "user_id",
    "username", 
    "comment_text",
    "post_url"
]

USER_FIELDS_OPTIONAL = [
    "full_name",
    "profile_pic_url",
    "is_private",
    "is_verified",
    "follower_count",
    "following_count",
    "media_count",
    "biography",
    "external_url",
    "is_business",
    "comment_time",
    "extraction_time"
]

USER_FIELDS_ALL = USER_FIELDS_REQUIRED + USER_FIELDS_OPTIONAL

# 状态常量
STATUS_SUCCESS = "success"
STATUS_ERROR = "error"
STATUS_PENDING = "pending"
STATUS_IN_PROGRESS = "in_progress"

# 错误代码常量
ERROR_AUTH_FAILED = "AUTH_FAILED"
ERROR_INVALID_SESSION = "INVALID_SESSION"
ERROR_INVALID_URI = "INVALID_URI"
ERROR_EXTRACTION_FAILED = "EXTRACTION_FAILED"
ERROR_STORAGE_FAILED = "STORAGE_FAILED"
ERROR_EXPORT_FAILED = "EXPORT_FAILED"
ERROR_RATE_LIMITED = "RATE_LIMITED"
ERROR_NETWORK_ERROR = "NETWORK_ERROR"

# 日志级别常量
LOG_LEVEL_DEBUG = "DEBUG"
LOG_LEVEL_INFO = "INFO"
LOG_LEVEL_WARNING = "WARNING"
LOG_LEVEL_ERROR = "ERROR"
LOG_LEVEL_CRITICAL = "CRITICAL"

# 文件路径常量
CONFIG_FILE_PATH = "config/config.yaml"
DATABASE_PATH = "data/database/users.db"
LOGS_PATH = "data/logs/"
EXPORTS_PATH = "data/exports/"
SESSIONS_PATH = "data/sessions/"

# 正则表达式常量
INSTAGRAM_POST_URL_PATTERN = r"https?://(?:www\.)?instagram\.com/p/([A-Za-z0-9_-]+)/?.*"
INSTAGRAM_REEL_URL_PATTERN = r"https?://(?:www\.)?instagram\.com/reel/([A-Za-z0-9_-]+)/?.*"

# 限制常量
MAX_BATCH_SIZE = 50
MAX_EXPORT_RECORDS = 100000
MAX_SESSION_AGE_SECONDS = 86400  # 24小时
MAX_MEMORY_USAGE_MB = 512

# UI常量
UI_ITEMS_PER_PAGE = 20
UI_MAX_URI_INPUT_LENGTH = 10000
UI_PROGRESS_UPDATE_INTERVAL = 1  # 秒
