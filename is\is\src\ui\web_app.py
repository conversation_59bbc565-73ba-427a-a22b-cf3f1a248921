"""
Flask Web应用模块
提供Web界面和API接口
"""

import os
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS

from config.settings import config_manager
from src.utils.logger import get_logger
from src.storage.models import db_manager, User
from src.utils.validators import DataValidator
from src.extractor.instagram_client import instagram_client
from src.extractor.task_manager import task_manager


def create_app():
    """
    创建Flask应用实例
    
    Returns:
        Flask: Flask应用实例
    """
    app = Flask(__name__, 
                template_folder='templates',
                static_folder='static')
    
    # 配置应用
    app_config = config_manager.get_app_config()
    app.config['SECRET_KEY'] = app_config.get('secret_key', 'dev-secret-key')
    app.config['DEBUG'] = app_config.get('debug', False)
    
    # 启用CORS
    CORS(app)
    
    # 获取日志记录器
    logger = get_logger('web_app')
    
    # 注册路由
    register_routes(app, logger)
    
    return app


def register_routes(app: Flask, logger):
    """
    注册路由
    
    Args:
        app: Flask应用实例
        logger: 日志记录器
    """
    
    @app.route('/')
    def index():
        """首页"""
        return render_template('index.html')
    
    @app.route('/results')
    def results():
        """结果页面"""
        return render_template('results.html')
    
    # API路由
    @app.route('/api/auth/test', methods=['POST'])
    def test_auth():
        """测试Session ID连接"""
        try:
            data = request.get_json()
            session_id = data.get('session_id', '').strip()

            if not session_id:
                return jsonify({
                    'success': False,
                    'message': '请提供Session ID'
                }), 400

            # 验证Session ID格式
            try:
                DataValidator.validate_session_id(session_id)
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'Session ID格式无效: {str(e)}'
                }), 400

            # 使用真实的Instagram API验证
            try:
                success = instagram_client.authenticate(session_id)
                if success:
                    user_info = instagram_client.get_current_user()
                    logger.info("Session ID验证成功", username=user_info.get('username'))

                    return jsonify({
                        'success': True,
                        'message': '连接成功',
                        'user_info': {
                            'username': user_info.get('username'),
                            'user_id': user_info.get('user_id'),
                            'full_name': user_info.get('full_name'),
                            'is_verified': user_info.get('is_verified')
                        }
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': '认证失败'
                    }), 401

            except Exception as e:
                logger.error(f"Instagram认证失败: {e}")
                return jsonify({
                    'success': False,
                    'message': f'认证失败: {str(e)}'
                }), 401

        except Exception as e:
            logger.error(f"Session ID验证失败: {e}")
            return jsonify({
                'success': False,
                'message': f'验证失败: {str(e)}'
            }), 500
    
    @app.route('/api/extract/start', methods=['POST'])
    def start_extraction():
        """开始数据提取"""
        try:
            data = request.get_json()
            session_id = data.get('session_id', '').strip()
            uris = data.get('uris', [])
            
            if not session_id:
                return jsonify({
                    'success': False,
                    'message': '请提供Session ID'
                }), 400
            
            if not uris:
                return jsonify({
                    'success': False,
                    'message': '请提供Instagram链接'
                }), 400
            
            # 验证URI列表
            valid_uris, invalid_uris = DataValidator.validate_uri_list(uris)
            
            if not valid_uris:
                return jsonify({
                    'success': False,
                    'message': '没有有效的Instagram链接'
                }), 400
            
            logger.info(f"开始数据提取",
                       valid_uris_count=len(valid_uris),
                       invalid_uris_count=len(invalid_uris))

            # 创建并启动后台提取任务
            task_id = task_manager.create_task(session_id, valid_uris)

            if task_manager.start_task(task_id):
                return jsonify({
                    'success': True,
                    'message': '提取任务已启动',
                    'task_id': task_id,
                    'valid_uris': len(valid_uris),
                    'invalid_uris': len(invalid_uris)
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '启动任务失败'
                }), 500
            
        except Exception as e:
            logger.error(f"启动数据提取失败: {e}")
            return jsonify({
                'success': False,
                'message': f'启动失败: {str(e)}'
            }), 500
    
    @app.route('/api/extract/progress', methods=['GET'])
    def get_progress():
        """获取提取进度"""
        try:
            # 获取最新的任务进度
            all_tasks = task_manager.get_all_tasks()

            if not all_tasks:
                return jsonify({
                    'success': True,
                    'progress': {
                        'status': 'no_task',
                        'total': 0,
                        'processed': 0,
                        'successful': 0,
                        'failed': 0,
                        'current_status': '没有运行中的任务',
                        'estimated_time': None
                    }
                })

            # 获取最新的任务
            latest_task = None
            for task_info in all_tasks.values():
                if latest_task is None:
                    latest_task = task_info
                elif (task_info['progress'].get('start_time') and
                      latest_task['progress'].get('start_time') and
                      task_info['progress']['start_time'] > latest_task['progress']['start_time']):
                    latest_task = task_info

            return jsonify({
                'success': True,
                'progress': latest_task['progress']
            })

        except Exception as e:
            logger.error(f"获取进度失败: {e}")
            return jsonify({
                'success': False,
                'message': f'获取进度失败: {str(e)}'
            }), 500
    
    @app.route('/api/extract/pause', methods=['POST'])
    def pause_extraction():
        """暂停提取"""
        try:
            data = request.get_json() or {}
            task_id = data.get('task_id')

            if task_id:
                success = task_manager.pause_task(task_id)
            else:
                # 暂停所有运行中的任务
                all_tasks = task_manager.get_all_tasks()
                success = False
                for tid, task_info in all_tasks.items():
                    if task_info['status'] == 'running':
                        task_manager.pause_task(tid)
                        success = True

            if success:
                logger.info("暂停数据提取", task_id=task_id)
                return jsonify({
                    'success': True,
                    'message': '已暂停提取'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '没有可暂停的任务'
                }), 400

        except Exception as e:
            logger.error(f"暂停提取失败: {e}")
            return jsonify({
                'success': False,
                'message': f'暂停失败: {str(e)}'
            }), 500

    @app.route('/api/extract/stop', methods=['POST'])
    def stop_extraction():
        """停止提取"""
        try:
            data = request.get_json() or {}
            task_id = data.get('task_id')

            if task_id:
                success = task_manager.stop_task(task_id)
            else:
                # 停止所有运行中的任务
                all_tasks = task_manager.get_all_tasks()
                success = False
                for tid, task_info in all_tasks.items():
                    if task_info['status'] in ['running', 'paused']:
                        task_manager.stop_task(tid)
                        success = True

            if success:
                logger.info("停止数据提取", task_id=task_id)
                return jsonify({
                    'success': True,
                    'message': '已停止提取'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '没有可停止的任务'
                }), 400

        except Exception as e:
            logger.error(f"停止提取失败: {e}")
            return jsonify({
                'success': False,
                'message': f'停止失败: {str(e)}'
            }), 500
    
    @app.route('/api/data/statistics', methods=['GET'])
    def get_statistics():
        """获取数据统计"""
        try:
            stats = db_manager.get_database_stats()
            
            return jsonify({
                'success': True,
                'stats': {
                    'total_users': stats['unique_users'],
                    'total_comments': stats['total_comments'],
                    'total_posts': stats['unique_posts'],
                    'verified_users': stats['verified_users'],
                    'database_size_mb': stats['database_size_mb'],
                    'latest_extraction': stats['latest_extraction']
                }
            })
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return jsonify({
                'success': False,
                'message': f'获取统计信息失败: {str(e)}'
            }), 500
    
    @app.route('/api/data/recent', methods=['GET'])
    def get_recent_data():
        """获取最近的数据"""
        try:
            limit = request.args.get('limit', 10, type=int)
            
            session = db_manager.get_session()
            try:
                from src.storage.models import User
                users = session.query(User).order_by(User.extraction_time.desc()).limit(limit).all()
                
                data = [user.to_dict() for user in users]
                
                return jsonify({
                    'success': True,
                    'data': data
                })
                
            finally:
                db_manager.close_session(session)
            
        except Exception as e:
            logger.error(f"获取最近数据失败: {e}")
            return jsonify({
                'success': False,
                'message': f'获取数据失败: {str(e)}'
            }), 500
    
    @app.route('/api/data/export', methods=['POST'])
    def export_data():
        """导出数据"""
        try:
            data = request.get_json()
            export_format = data.get('format', 'csv')
            limit = data.get('limit', 1000)

            logger.info(f"导出数据请求", format=export_format, limit=limit)

            # 获取数据
            session = db_manager.get_session()
            try:
                query = session.query(User).order_by(User.extraction_time.desc())
                if limit > 0:
                    query = query.limit(limit)
                users = query.all()

                if not users:
                    return jsonify({
                        'success': False,
                        'message': '没有数据可导出'
                    }), 400

                # 创建导出目录（使用绝对路径）
                from pathlib import Path
                project_root = Path(__file__).parent.parent.parent  # 回到项目根目录
                export_dir = project_root / 'data' / 'exports'
                export_dir.mkdir(parents=True, exist_ok=True)

                # 生成文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"instagram_data_{timestamp}.{export_format}"
                filepath = export_dir / filename

                if export_format.lower() == 'csv':
                    # 导出CSV格式
                    import csv
                    with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                        fieldnames = ['user_id', 'username', 'full_name', 'comment_text',
                                    'comment_time', 'post_url', 'is_private', 'is_verified',
                                    'extraction_time']
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()

                        for user in users:
                            writer.writerow({
                                'user_id': user.user_id,
                                'username': user.username,
                                'full_name': user.full_name or '',
                                'comment_text': user.comment_text or '',
                                'comment_time': user.comment_time.isoformat() if user.comment_time else '',
                                'post_url': user.post_url or '',
                                'is_private': user.is_private,
                                'is_verified': user.is_verified,
                                'extraction_time': user.extraction_time.isoformat() if user.extraction_time else ''
                            })

                elif export_format.lower() == 'json':
                    # 导出JSON格式
                    import json
                    export_data = []
                    for user in users:
                        export_data.append({
                            'user_id': user.user_id,
                            'username': user.username,
                            'full_name': user.full_name,
                            'comment_text': user.comment_text,
                            'comment_time': user.comment_time.isoformat() if user.comment_time else None,
                            'post_url': user.post_url,
                            'is_private': user.is_private,
                            'is_verified': user.is_verified,
                            'extraction_time': user.extraction_time.isoformat() if user.extraction_time else None
                        })

                    with open(filepath, 'w', encoding='utf-8') as jsonfile:
                        json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                logger.info(f"导出文件创建成功", filename=filename, record_count=len(users))

                return jsonify({
                    'success': True,
                    'message': f'导出成功，共{len(users)}条记录',
                    'filename': filename,
                    'download_url': f'/api/download/{filename}',
                    'record_count': len(users)
                })

            finally:
                db_manager.close_session(session)

        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return jsonify({
                'success': False,
                'message': f'导出失败: {str(e)}'
            }), 500

    @app.route('/api/data/delete', methods=['POST'])
    def delete_users():
        """删除选中的用户数据"""
        try:
            data = request.get_json()
            user_ids = data.get('user_ids', [])

            if not user_ids:
                return jsonify({
                    'success': False,
                    'message': '请选择要删除的用户'
                }), 400

            logger.info(f"删除用户数据请求", user_count=len(user_ids))

            # 删除数据
            session = db_manager.get_session()
            try:
                # 查询要删除的用户（用于日志记录）
                users_to_delete = session.query(User).filter(User.id.in_(user_ids)).all()

                if not users_to_delete:
                    return jsonify({
                        'success': False,
                        'message': '未找到要删除的用户数据'
                    }), 404

                # 记录删除的用户信息
                deleted_usernames = [user.username for user in users_to_delete]

                # 执行删除
                deleted_count = session.query(User).filter(User.id.in_(user_ids)).delete(synchronize_session=False)
                session.commit()

                logger.info(f"用户数据删除成功",
                           deleted_count=deleted_count,
                           usernames=deleted_usernames[:5])  # 只记录前5个用户名

                return jsonify({
                    'success': True,
                    'message': f'成功删除 {deleted_count} 条用户数据',
                    'deleted_count': deleted_count
                })

            except Exception as e:
                session.rollback()
                logger.error(f"删除用户数据失败: {e}")
                return jsonify({
                    'success': False,
                    'message': f'删除失败: {str(e)}'
                }), 500
            finally:
                db_manager.close_session(session)

        except Exception as e:
            logger.error(f"删除用户数据请求处理失败: {e}")
            return jsonify({
                'success': False,
                'message': f'请求处理失败: {str(e)}'
            }), 500

    @app.route('/api/download/<filename>')
    def download_file(filename):
        """下载导出的文件"""
        try:
            from pathlib import Path
            project_root = Path(__file__).parent.parent.parent  # 回到项目根目录
            export_dir = project_root / 'data' / 'exports'
            filepath = export_dir / filename

            # 检查文件是否存在
            if not filepath.exists():
                logger.error(f"下载文件不存在", filename=filename)
                return jsonify({
                    'success': False,
                    'message': '文件不存在'
                }), 404

            # 检查文件安全性（防止路径遍历攻击）
            if not str(filepath.resolve()).startswith(str(export_dir.resolve())):
                logger.error(f"不安全的文件路径", filename=filename)
                return jsonify({
                    'success': False,
                    'message': '无效的文件路径'
                }), 400

            logger.info(f"开始下载文件", filename=filename)

            # 发送文件
            return send_file(
                str(filepath),
                as_attachment=True,
                download_name=filename,
                mimetype='application/octet-stream'
            )

        except Exception as e:
            logger.error(f"下载文件失败: {e}", filename=filename)
            return jsonify({
                'success': False,
                'message': f'下载失败: {str(e)}'
            }), 500

    @app.route('/api/data/list', methods=['GET'])
    def get_data_list():
        """获取数据列表"""
        try:
            # 获取查询参数
            page = request.args.get('page', 1, type=int)
            page_size = request.args.get('page_size', 20, type=int)
            sort_by = request.args.get('sort_by', 'extraction_time')
            sort_order = request.args.get('sort_order', 'desc')
            search = request.args.get('search', '').strip()
            is_verified = request.args.get('is_verified')
            is_private = request.args.get('is_private')

            session = db_manager.get_session()
            try:
                from src.storage.models import User
                from sqlalchemy import desc, asc, or_

                # 构建查询
                query = session.query(User)

                # 搜索过滤
                if search:
                    query = query.filter(or_(
                        User.username.contains(search),
                        User.full_name.contains(search),
                        User.comment_text.contains(search)
                    ))

                # 认证状态过滤
                if is_verified is not None:
                    query = query.filter(User.is_verified == (is_verified.lower() == 'true'))

                # 账户类型过滤
                if is_private is not None:
                    query = query.filter(User.is_private == (is_private.lower() == 'true'))

                # 排序
                if hasattr(User, sort_by):
                    order_func = desc if sort_order == 'desc' else asc
                    query = query.order_by(order_func(getattr(User, sort_by)))

                # 分页
                total = query.count()
                offset = (page - 1) * page_size
                users = query.offset(offset).limit(page_size).all()

                # 转换为字典
                data = [user.to_dict() for user in users]

                return jsonify({
                    'success': True,
                    'data': data,
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': (total + page_size - 1) // page_size
                })

            finally:
                db_manager.close_session(session)

        except Exception as e:
            logger.error(f"获取数据列表失败: {e}")
            return jsonify({
                'success': False,
                'message': f'获取数据失败: {str(e)}'
            }), 500

    @app.route('/api/data/clear', methods=['POST'])
    def clear_data():
        """清空数据"""
        try:
            session = db_manager.get_session()
            try:
                from src.storage.models import User
                deleted_count = session.query(User).count()
                session.query(User).delete()
                session.commit()

                logger.info(f"清空数据", deleted_count=deleted_count)

                return jsonify({
                    'success': True,
                    'message': f'已清空 {deleted_count} 条记录'
                })

            finally:
                db_manager.close_session(session)

        except Exception as e:
            logger.error(f"清空数据失败: {e}")
            return jsonify({
                'success': False,
                'message': f'清空失败: {str(e)}'
            }), 500
    
    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        return jsonify({
            'success': False,
            'message': '页面不存在'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        logger.error(f"内部服务器错误: {error}")
        return jsonify({
            'success': False,
            'message': '内部服务器错误'
        }), 500
