/* Instagram评论提取器自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #405de6;
    --secondary-color: #5851db;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #fafafa;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255,255,255,0.8) !important;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.btn-primary:hover {
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(64, 93, 230, 0.25);
}

.input-group .btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.input-group .form-control {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

/* 进度条样式 */
.progress {
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.status-connected {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-disconnected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-testing {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 统计卡片 */
.stat-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    background: white;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 表格样式 */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    user-select: none;
}

.table th:hover {
    background-color: #e9ecef;
}

.table td {
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

.table-responsive {
    border-radius: var(--border-radius);
}

/* 徽章样式 */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 300px;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* 手风琴样式 */
.accordion-button {
    font-weight: 500;
}

.accordion-button:not(.collapsed) {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
    }
    
    .d-grid .btn {
        margin-bottom: 0.5rem;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
}

/* 分页样式 */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 1px solid #ddd;
    color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: var(--light-color);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
