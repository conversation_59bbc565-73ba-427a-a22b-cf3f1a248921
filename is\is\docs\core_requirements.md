# 核心功能需求定义

## 系统概述
设计一个模块化、易扩展的Instagram评论用户账号保存系统，支持批量处理、数据存储和导出功能。

## 1. 核心功能模块

### 1.1 身份验证模块 (Authentication Module)
**功能**: 使用Instagram session ID进行安全身份验证

**具体需求**:
- 支持session ID输入和验证
- 安全存储session信息
- 自动检测session有效性
- 支持session过期重新登录
- 错误处理和用户提示

**输入参数**:
- `session_id`: Instagram session ID字符串
- `proxy_config`: 代理配置（可选）

**输出**:
- `auth_status`: 认证状态（成功/失败）
- `user_info`: 当前登录用户信息
- `error_message`: 错误信息（如有）

### 1.2 URI处理模块 (URI Processing Module)
**功能**: 接受和处理多个Instagram帖子URI

**具体需求**:
- 支持单个URI输入
- 支持批量URI输入（逗号分隔、换行分隔）
- URI格式验证和标准化
- 提取帖子ID和媒体信息
- 去重处理

**支持的URI格式**:
```
https://www.instagram.com/p/DMcnC6ZTroi/
https://instagram.com/p/DMcnC6ZTroi/
https://www.instagram.com/p/DMcnC6ZTroi/?utm_source=...
```

**输入参数**:
- `uri_list`: URI列表或字符串
- `validation_mode`: 验证模式（严格/宽松）

**输出**:
- `valid_uris`: 有效URI列表
- `invalid_uris`: 无效URI列表
- `media_ids`: 提取的媒体ID列表

### 1.3 数据提取模块 (Data Extraction Module)
**功能**: 获取指定帖子的评论和用户资料信息

**具体需求**:
- 获取帖子所有评论
- 提取评论者完整用户资料
- 支持分批处理（每10-20条保存一次）
- 数据去重和验证
- 进度跟踪和状态更新

**提取的用户字段**:
```python
{
    'user_id': int,           # 用户ID（发送DM必需）
    'username': str,          # 用户名
    'full_name': str,         # 全名
    'profile_pic_url': str,   # 头像URL
    'is_private': bool,       # 是否私人账户
    'is_verified': bool,      # 是否认证账户
    'follower_count': int,    # 粉丝数
    'following_count': int,   # 关注数
    'media_count': int,       # 帖子数
    'biography': str,         # 个人简介
    'external_url': str,      # 外部链接
    'is_business': bool,      # 是否商业账户
    'comment_text': str,      # 评论内容
    'comment_time': datetime, # 评论时间
    'post_url': str,          # 来源帖子URL
    'extraction_time': datetime # 提取时间
}
```

**输入参数**:
- `media_ids`: 媒体ID列表
- `batch_size`: 批处理大小（默认10）
- `include_replies`: 是否包含回复评论

**输出**:
- `user_data`: 用户数据列表
- `extraction_stats`: 提取统计信息
- `failed_items`: 失败项目列表

### 1.4 数据存储模块 (Data Storage Module)
**功能**: 管理用户数据的存储和检索

**具体需求**:
- SQLite数据库存储
- 数据去重（基于user_id + post_url）
- 支持增量更新
- 数据完整性验证
- 备份和恢复功能

**数据库表结构**:
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    username TEXT NOT NULL,
    full_name TEXT,
    profile_pic_url TEXT,
    is_private BOOLEAN,
    is_verified BOOLEAN,
    follower_count INTEGER,
    following_count INTEGER,
    media_count INTEGER,
    biography TEXT,
    external_url TEXT,
    is_business BOOLEAN,
    comment_text TEXT,
    comment_time DATETIME,
    post_url TEXT NOT NULL,
    extraction_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, post_url)
);
```

**输入参数**:
- `user_data`: 用户数据字典或列表
- `update_mode`: 更新模式（插入/更新/忽略）

**输出**:
- `saved_count`: 保存成功数量
- `updated_count`: 更新数量
- `duplicate_count`: 重复数量

### 1.5 数据导出模块 (Data Export Module)
**功能**: 提供灵活的数据导出功能

**具体需求**:
- 支持多种导出格式（JSON、CSV、Excel）
- 用户可选择导出字段
- 支持筛选条件（时间范围、帖子URL等）
- 支持排序（按时间、用户名等）
- 导出进度显示

**导出格式选项**:
- **JSON**: 完整数据结构
- **CSV**: 表格格式，适合Excel打开
- **Excel**: 多工作表，包含统计信息

**可选导出字段**:
- 基础信息：user_id, username, full_name
- 社交数据：follower_count, following_count, is_verified
- 评论信息：comment_text, comment_time, post_url
- 完整信息：所有字段

**输入参数**:
- `export_format`: 导出格式
- `selected_fields`: 选择的字段列表
- `filter_conditions`: 筛选条件
- `sort_options`: 排序选项

**输出**:
- `export_file_path`: 导出文件路径
- `export_stats`: 导出统计信息

## 2. 系统特性要求

### 2.1 模块化设计
- 每个模块独立开发和测试
- 清晰的接口定义
- 松耦合架构
- 易于扩展和维护

### 2.2 错误处理
- 全局异常捕获
- 详细错误日志
- 用户友好的错误提示
- 自动重试机制（网络错误）

### 2.3 性能要求
- 支持大量数据处理（1000+用户）
- 内存使用优化
- 数据库查询优化
- 响应时间 < 3秒（单次操作）

### 2.4 安全要求
- Session ID安全存储
- 数据传输加密
- 用户隐私保护
- 访问权限控制

## 3. 用户交互流程

### 3.1 基本工作流程
1. **用户输入session ID** → 系统验证身份
2. **用户输入帖子URI** → 系统验证和处理URI
3. **点击开始分析** → 系统开始数据提取
4. **显示进度** → 实时更新提取状态
5. **展示结果** → 表格形式显示用户数据
6. **数据导出** → 用户选择格式和字段导出

### 3.2 批处理策略
- 每处理10-20个用户保存一次数据库
- 遇到无法获取的数据直接跳过
- 显示实时进度和统计信息
- 支持暂停和恢复功能

### 3.3 数据优先级
1. **必须获取**: user_id, username, comment_text, post_url
2. **重要获取**: full_name, follower_count, is_verified
3. **可选获取**: biography, external_url, profile_pic_url

## 4. 技术约束

### 4.1 API限制遵守
- 严格遵循Instagram API速率限制
- 实现智能延迟机制
- 使用代理轮换（如需要）
- 监控API响应状态

### 4.2 数据完整性
- 数据验证和清洗
- 重复数据检测
- 数据一致性检查
- 定期数据备份

### 4.3 扩展性考虑
- 支持添加新的社交平台
- 支持新的数据字段
- 支持新的导出格式
- 支持高级分析功能

## 5. 成功标准

### 5.1 功能完整性
- 所有核心功能正常工作
- 错误处理覆盖所有场景
- 用户界面直观易用
- 数据导出格式正确

### 5.2 性能指标
- 处理1000个评论 < 10分钟
- 数据库查询响应 < 1秒
- 内存使用 < 500MB
- 成功率 > 95%

### 5.3 稳定性要求
- 连续运行24小时无崩溃
- 网络中断自动恢复
- 数据不丢失
- 日志记录完整
