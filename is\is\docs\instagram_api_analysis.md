# Instagram API 详细分析报告

## 概述
本文档基于 `instagrapi` 库（最新版本2025年）对Instagram私有API进行详细分析，为项目开发提供技术基础。

## 1. 推荐库：instagrapi
- **GitHub**: https://github.com/subzeroid/instagrapi
- **特点**: 最快速、最强大的Instagram私有API Python库
- **支持**: Python >= 3.9
- **最新更新**: 2025年5月25日（最新逆向工程检查）

## 2. 身份验证方法

### 2.1 Session ID 登录（推荐）
```python
from instagrapi import Client

cl = Client()
cl.login_by_sessionid("<your_sessionid>")
```

**Session ID 格式示例**: `75995718761%3AViYvzu2bzc1AnC%3A12%3AAYesnOHlWUI2FjpGK4GMGb8bM_llxyWUuzPGEvVeTA`

### 2.2 用户名密码登录
```python
cl = Client()
cl.login(USERNAME, PASSWORD)
# 支持2FA验证码
cl.login(USERNAME, PASSWORD, verification_code="123456")
```

### 2.3 会话持久化（重要）
```python
# 首次登录并保存会话
cl = Client()
cl.login(USERNAME, PASSWORD)
cl.dump_settings("session.json")

# 后续使用保存的会话
cl = Client()
cl.load_settings("session.json")
cl.login(USERNAME, PASSWORD)  # 使用会话，不会重新登录
```

## 3. 评论检索功能

### 3.1 获取帖子评论
```python
# 获取指定帖子的所有评论
media_id = cl.media_id(cl.media_pk_from_url('https://www.instagram.com/p/DMcnC6ZTroi/'))
comments = cl.media_comments(media_id, amount=0)  # amount=0 获取所有评论

# 分块获取评论（推荐用于大量评论）
comments_chunk, next_cursor = cl.media_comments_chunk(media_id, max_amount=100)
```

### 3.2 评论数据结构
```python
# Comment对象包含以下字段：
{
    'pk': 17926777897585108,           # 评论ID
    'text': 'Test comment',           # 评论内容
    'user': {                         # 评论者信息
        'pk': 1903424587,            # 用户ID
        'username': 'example',        # 用户名
        'full_name': 'Example Name',  # 全名
        'profile_pic_url': '...',     # 头像URL
    },
    'created_at_utc': datetime,       # 创建时间
    'like_count': 0,                  # 点赞数
    'has_liked': False               # 是否已点赞
}
```

## 4. 用户资料提取

### 4.1 获取用户完整信息
```python
# 通过用户名获取用户信息
user_info = cl.user_info_by_username('username')

# 通过用户ID获取用户信息
user_info = cl.user_info(user_id)
```

### 4.2 用户数据结构
```python
# User对象包含以下字段：
{
    'pk': 1903424587,                 # 用户ID（发送DM必需）
    'username': 'example',            # 用户名
    'full_name': 'Example Example',   # 全名
    'is_private': False,              # 是否私人账户
    'profile_pic_url': '...',         # 头像URL
    'is_verified': False,             # 是否认证
    'media_count': 102,               # 帖子数量
    'follower_count': 576,            # 粉丝数
    'following_count': 538,           # 关注数
    'biography': 'Bio text',          # 个人简介
    'external_url': '...',            # 外部链接
    'is_business': False              # 是否商业账户
}
```

## 5. 私信（DM）功能

### 5.1 发送私信
```python
# 发送文本消息
cl.direct_send('Hello!', user_ids=[user_id])

# 发送给多个用户
cl.direct_send('Hello!', user_ids=[user_id1, user_id2])

# 发送到现有对话
cl.direct_answer(thread_id, 'Reply message')
```

### 5.2 所需参数
- **user_id**: 目标用户的数字ID（从用户资料获取）
- **message**: 消息内容（字符串）
- **thread_id**: 对话ID（可选，用于回复现有对话）

## 6. 速率限制和最佳实践

### 6.1 安全限制建议
- **每IP地址**: 最多10个账户
- **每账户发帖**: 4-16个帖子/天
- **每账户故事**: 24-48个故事/天
- **请求间隔**: 1-3秒随机延迟

### 6.2 实现延迟控制
```python
cl = Client()
cl.delay_range = [1, 3]  # 每次请求后随机延迟1-3秒
```

### 6.3 代理设置（强烈推荐）
```python
cl.set_proxy("http://username:<EMAIL>:8080")
cl.set_proxy("socks5://username:<EMAIL>:1080")
```

## 7. 反检测措施

### 7.1 设备模拟
```python
# 设置设备信息
cl.set_device({
    "cpu": "h1",
    "dpi": "640dpi", 
    "model": "h1",
    "device": "RS988",
    "resolution": "1440x2392",
    "manufacturer": "LGE/lge"
})
```

### 7.2 地理位置设置
```python
cl.set_country("US")           # 设置国家
cl.set_country_code(1)         # 设置国家代码
cl.set_locale("en_US")         # 设置语言
cl.set_timezone_offset(-7*3600) # 设置时区偏移
```

### 7.3 用户代理设置
```python
cl.set_user_agent("Instagram **********.172 Android ...")
```

## 8. 错误处理

### 8.1 常见异常
- `LoginRequired`: 需要重新登录
- `ChallengeRequired`: 需要验证挑战
- `RateLimitError`: 请求频率过高
- `PrivateError`: 访问私人内容

### 8.2 挑战解决器
```python
# 内置邮箱和短信挑战处理器
cl.challenge_code_handler = lambda username, choice: input("Enter code: ")
```

## 9. 数据提取工作流程

### 9.1 完整流程示例
```python
# 1. 登录
cl = Client()
cl.login_by_sessionid(session_id)

# 2. 获取帖子ID
media_pk = cl.media_pk_from_url(post_url)

# 3. 获取评论
comments = cl.media_comments(media_pk, amount=0)

# 4. 提取用户信息
for comment in comments:
    user_id = comment.user.pk
    user_info = cl.user_info(user_id)
    # 存储用户信息...
```

## 10. 重要注意事项

1. **始终使用会话持久化**避免频繁登录
2. **添加随机延迟**模拟真实用户行为  
3. **使用代理服务器**避免IP封禁
4. **批量处理数据**每10-20条记录保存一次
5. **错误处理**遇到无法获取的数据直接跳过
6. **遵守速率限制**避免账户被封

## 11. 推荐工具和服务

- **代理服务**: SOAX (https://soax.com)
- **SaaS服务**: HikerAPI (用于商业用途)
- **异步版本**: aiograpi (高并发需求)
