#!/usr/bin/env python3
"""
Instagram评论提取器主应用入口
运行命令：python main.py
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import config_manager
from src.utils.logger import get_logger
from src.storage.models import db_manager
from src.ui.web_app import create_app


def setup_environment():
    """设置运行环境"""
    # 确保必要的目录存在
    directories = [
        'data/database',
        'data/exports', 
        'data/logs',
        'data/sessions'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # 初始化日志记录器
    logger_config = config_manager.get_logging_config()
    logger = get_logger('main', logger_config)
    logger.info("应用启动中...")
    
    # 初始化数据库
    try:
        db_manager.create_tables()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise
    
    return logger


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='Instagram评论提取器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 启动Web界面（默认）
  python main.py --host 0.0.0.0     # 允许外部访问
  python main.py --port 8080        # 指定端口
  python main.py --debug            # 启用调试模式
  python main.py --config custom.yaml  # 使用自定义配置文件
        """
    )
    
    parser.add_argument(
        '--host',
        default=None,
        help='服务器主机地址（默认从配置文件读取）'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=None,
        help='服务器端口（默认从配置文件读取）'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--config',
        default='config/config.yaml',
        help='配置文件路径（默认: config/config.yaml）'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Instagram评论提取器 v1.0.0'
    )
    
    return parser


def validate_config():
    """验证配置文件"""
    try:
        # 检查必要的配置项
        app_config = config_manager.get_app_config()
        if not app_config:
            raise ValueError("缺少应用配置")
        
        instagram_config = config_manager.get_instagram_config()
        if not instagram_config:
            raise ValueError("缺少Instagram API配置")
        
        database_config = config_manager.get_database_config()
        if not database_config:
            raise ValueError("缺少数据库配置")
        
        return True
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False


def run_web_app(host=None, port=None, debug=False):
    """运行Web应用"""
    logger = get_logger('main')
    
    try:
        # 创建Flask应用
        app = create_app()
        
        # 从配置文件或命令行参数获取运行参数
        app_config = config_manager.get_app_config()
        
        run_host = host or app_config.get('host', '127.0.0.1')
        run_port = port or app_config.get('port', 5000)
        run_debug = debug or app_config.get('debug', False)
        
        logger.info(f"启动Web服务器: http://{run_host}:{run_port}")
        logger.info(f"调试模式: {'开启' if run_debug else '关闭'}")
        
        # 显示启动信息
        print("\n" + "="*60)
        print("🚀 Instagram评论提取器已启动")
        print("="*60)
        print(f"📱 访问地址: http://{run_host}:{run_port}")
        print(f"🔧 调试模式: {'开启' if run_debug else '关闭'}")
        print(f"📁 数据目录: {Path('data').absolute()}")
        print(f"📋 配置文件: {config_manager._config_path}")
        print("="*60)
        print("💡 使用提示:")
        print("   1. 在浏览器中打开上述地址")
        print("   2. 输入Instagram Session ID")
        print("   3. 添加要分析的帖子链接")
        print("   4. 点击'开始分析'按钮")
        print("   5. 查看提取结果并导出数据")
        print("="*60)
        print("⚠️  注意事项:")
        print("   - 请遵守Instagram使用条款")
        print("   - 建议使用适当的请求间隔")
        print("   - 定期备份重要数据")
        print("="*60)
        print("按 Ctrl+C 停止服务器\n")
        
        # 启动Flask应用
        app.run(
            host=run_host,
            port=run_port,
            debug=run_debug,
            threaded=True,
            use_reloader=False  # 避免重复启动
        )
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭服务器...")
        print("\n👋 服务器已停止")
    except Exception as e:
        logger.error(f"启动Web应用失败: {e}")
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)


def show_system_info():
    """显示系统信息"""
    logger = get_logger('main')
    
    try:
        # 获取数据库统计信息
        stats = db_manager.get_database_stats()
        
        print("\n" + "="*50)
        print("📊 系统状态")
        print("="*50)
        print(f"数据库记录数: {stats['total_records']}")
        print(f"唯一用户数: {stats['unique_users']}")
        print(f"总评论数: {stats['total_comments']}")
        print(f"唯一帖子数: {stats['unique_posts']}")
        print(f"认证用户数: {stats['verified_users']}")
        print(f"数据库大小: {stats['database_size_mb']:.2f} MB")
        if stats['latest_extraction']:
            print(f"最近提取: {stats['latest_extraction']}")
        print("="*50)
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        print(f"❌ 获取系统信息失败: {e}")


def main():
    """主函数"""
    # 解析命令行参数
    parser = create_argument_parser()
    args = parser.parse_args()
    
    try:
        # 加载配置文件
        if args.config and Path(args.config).exists():
            config_manager.load_config(args.config)
        elif not Path(config_manager._config_path).exists():
            print(f"❌ 配置文件不存在: {config_manager._config_path}")
            print("请确保配置文件存在或使用 --config 参数指定配置文件路径")
            sys.exit(1)
        
        # 验证配置
        if not validate_config():
            sys.exit(1)
        
        # 设置环境
        logger = setup_environment()
        
        # 显示系统信息
        show_system_info()
        
        # 运行Web应用
        run_web_app(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
        
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
