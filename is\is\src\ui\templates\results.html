{% extends "base.html" %}

{% block title %}数据管理 - Instagram评论提取器{% endblock %}

{% block content %}
<div class="row">
    <!-- 主要内容区域 -->
    <div class="col-lg-12">
        <!-- 统计信息面板 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                        <h4 class="card-title" id="totalUsersCount">0</h4>
                        <p class="card-text text-muted">总用户数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-comments fa-2x text-success mb-2"></i>
                        <h4 class="card-title" id="totalCommentsCount">0</h4>
                        <p class="card-text text-muted">总评论数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-link fa-2x text-info mb-2"></i>
                        <h4 class="card-title" id="totalPostsCount">0</h4>
                        <p class="card-text text-muted">总帖子数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-warning mb-2"></i>
                        <h4 class="card-title" id="verifiedUsersCount">0</h4>
                        <p class="card-text text-muted">认证用户数</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索工具栏 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>数据筛选与搜索
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="searchInput" class="form-label">搜索</label>
                            <input type="text" class="form-control" id="searchInput" 
                                   placeholder="搜索用户名、评论内容...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="filterVerified" class="form-label">认证状态</label>
                            <select class="form-select" id="filterVerified">
                                <option value="">所有用户</option>
                                <option value="true">已认证</option>
                                <option value="false">未认证</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="filterPrivate" class="form-label">账户类型</label>
                            <select class="form-select" id="filterPrivate">
                                <option value="">所有账户</option>
                                <option value="true">私人账户</option>
                                <option value="false">公开账户</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="sortBy" class="form-label">排序方式</label>
                            <select class="form-select" id="sortBy">
                                <option value="extraction_time">提取时间</option>
                                <option value="username">用户名</option>
                                <option value="follower_count">粉丝数</option>
                                <option value="comment_time">评论时间</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label for="sortOrder" class="form-label">排序顺序</label>
                            <select class="form-select" id="sortOrder">
                                <option value="desc">降序</option>
                                <option value="asc">升序</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-primary" id="applyFilters">
                            <i class="fas fa-search me-1"></i>应用筛选
                        </button>
                        <button type="button" class="btn btn-secondary" id="clearFilters">
                            <i class="fas fa-times me-1"></i>清空筛选
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group">
                            <button type="button" class="btn btn-success dropdown-toggle" 
                                    data-bs-toggle="dropdown" id="exportButton">
                                <i class="fas fa-download me-1"></i>导出数据
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-format="csv">
                                    <i class="fas fa-file-csv me-2"></i>CSV格式
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-format="json">
                                    <i class="fas fa-file-code me-2"></i>JSON格式
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-format="excel">
                                    <i class="fas fa-file-excel me-2"></i>Excel格式
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>用户数据
                </h5>
                <div>
                    <span class="badge bg-primary" id="totalRecords">0 条记录</span>
                </div>
            </div>

            <!-- 批量操作工具栏 -->
            <div class="card-body border-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <span class="me-2 text-muted">已选择:</span>
                            <span class="badge bg-info me-3" id="selectedCount">0</span>
                            <button type="button" class="btn btn-danger btn-sm" id="deleteSelectedBtn" disabled>
                                <i class="fas fa-trash me-1"></i>删除选中
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="selectAllBtn">
                            <i class="fas fa-check-square me-1"></i>全选
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="deselectAllBtn">
                            <i class="fas fa-square me-1"></i>取消全选
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="dataTable">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>用户名</th>
                                <th>全名</th>
                                <th>粉丝数</th>
                                <th>认证</th>
                                <th>私人</th>
                                <th>评论内容</th>
                                <th>评论时间</th>
                                <th>提取时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- 动态生成的数据行 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载数据...</p>
                </div>
                
                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无数据</h5>
                    <p class="text-muted">还没有提取任何用户数据</p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>开始提取数据
                    </a>
                </div>
            </div>
        </div>

        <!-- 分页控制 -->
        <div class="card mt-3">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <span class="me-2">每页显示:</span>
                            <select class="form-select form-select-sm" id="pageSize" style="width: auto;">
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span class="ms-3" id="pageInfo">第 1 页，共 1 页</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <nav>
                            <ul class="pagination justify-content-end mb-0">
                                <li class="page-item" id="prevPageItem">
                                    <a class="page-link" href="#" id="prevPage">
                                        <i class="fas fa-chevron-left"></i> 上一页
                                    </a>
                                </li>
                                <li class="page-item" id="nextPageItem">
                                    <a class="page-link" href="#" id="nextPage">
                                        下一页 <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 动态生成的用户详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="sendDMButton">
                    <i class="fas fa-envelope me-1"></i>发送私信
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/results.js') }}"></script>
{% endblock %}
