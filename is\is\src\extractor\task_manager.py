"""
任务管理模块
管理后台数据提取任务
"""

import threading
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from enum import Enum

from src.extractor.instagram_client import instagram_client
from src.storage.models import db_manager, User
from src.utils.logger import get_logger
from src.utils.helpers import DataHelper


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"


class ExtractionTask:
    """数据提取任务"""
    
    def __init__(self, task_id: str, session_id: str, uris: List[str]):
        """
        初始化提取任务
        
        Args:
            task_id: 任务ID
            session_id: Instagram Session ID
            uris: 要处理的URI列表
        """
        self.task_id = task_id
        self.session_id = session_id
        self.uris = uris
        self.status = TaskStatus.PENDING
        self.progress = {
            'total': len(uris),
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'current_status': '准备开始...',
            'start_time': None,
            'end_time': None,
            'estimated_time': None,
            'errors': []
        }
        self.extracted_users = []
        self.should_stop = False
        self.should_pause = False
        self.logger = get_logger(f'task_{task_id}')
    
    def start(self):
        """启动任务"""
        try:
            self.status = TaskStatus.RUNNING
            self.progress['start_time'] = datetime.now()
            self.progress['current_status'] = '正在认证...'
            
            self.logger.info(f"任务开始", task_id=self.task_id, total_uris=len(self.uris))
            
            # 认证Instagram
            if not instagram_client.authenticate(self.session_id):
                raise Exception("Instagram认证失败")
            
            self.progress['current_status'] = '认证成功，开始提取数据...'
            
            # 处理每个URI
            for i, uri in enumerate(self.uris):
                if self.should_stop:
                    self.status = TaskStatus.STOPPED
                    self.progress['current_status'] = '任务已停止'
                    break
                
                while self.should_pause:
                    self.status = TaskStatus.PAUSED
                    self.progress['current_status'] = '任务已暂停'
                    time.sleep(1)
                
                if self.should_stop:
                    break
                
                self.status = TaskStatus.RUNNING
                self.progress['current_status'] = f'正在处理第{i+1}个链接: {uri}'
                
                try:
                    # 提取用户数据
                    self.logger.info(f"开始提取URI: {uri}")
                    user_data_list = instagram_client.extract_media_comments(uri)

                    # 立即保存到数据库
                    if user_data_list:
                        save_result = self._save_users_batch(user_data_list)
                        self.extracted_users.extend(user_data_list)
                        self.logger.info(f"数据保存成功",
                                       extracted_count=len(user_data_list),
                                       saved_count=save_result)
                    else:
                        self.logger.warning(f"未提取到用户数据", uri=uri)

                    self.progress['successful'] += 1
                    self.logger.info(f"URI处理成功", uri=uri, user_count=len(user_data_list))
                    
                except Exception as e:
                    self.progress['failed'] += 1
                    error_msg = f"处理URI失败: {uri} - {str(e)}"
                    self.progress['errors'].append(error_msg)
                    self.logger.error(error_msg, uri=uri, error=str(e))
                
                finally:
                    self.progress['processed'] += 1
                    self._update_estimated_time()
            
            # 任务完成
            if not self.should_stop:
                self.status = TaskStatus.COMPLETED
                self.progress['current_status'] = '任务完成'
            
            self.progress['end_time'] = datetime.now()
            
            self.logger.info(f"任务结束", 
                           task_id=self.task_id,
                           status=self.status.value,
                           total_users=len(self.extracted_users),
                           successful=self.progress['successful'],
                           failed=self.progress['failed'])
            
        except Exception as e:
            self.status = TaskStatus.FAILED
            self.progress['current_status'] = f'任务失败: {str(e)}'
            self.progress['end_time'] = datetime.now()
            self.logger.error(f"任务执行失败", task_id=self.task_id, error=str(e))
    
    def _save_users_batch(self, user_data_list: List):
        """批量保存用户数据"""
        saved_count = 0
        try:
            session = db_manager.get_session()
            try:
                for user_data in user_data_list:
                    try:
                        # 检查是否已存在（基于user_id和post_url的组合）
                        existing = session.query(User).filter(
                            User.user_id == user_data.user_id,
                            User.post_url == user_data.post_url,
                            User.comment_text == user_data.comment_text
                        ).first()

                        if not existing:
                            user = User.from_user_data(user_data)
                            session.add(user)
                            saved_count += 1
                            self.logger.debug(f"添加用户数据",
                                            username=user_data.username,
                                            user_id=user_data.user_id)
                        else:
                            self.logger.debug(f"用户数据已存在，跳过",
                                            username=user_data.username,
                                            user_id=user_data.user_id)

                    except Exception as e:
                        self.logger.warning(f"处理单个用户数据失败",
                                          username=getattr(user_data, 'username', 'unknown'),
                                          user_id=getattr(user_data, 'user_id', 'unknown'),
                                          error=str(e))
                        continue

                # 提交事务
                session.commit()
                self.logger.info(f"批量保存用户数据成功",
                               total_users=len(user_data_list),
                               saved_count=saved_count,
                               skipped_count=len(user_data_list) - saved_count)

            except Exception as e:
                session.rollback()
                self.logger.error(f"数据库事务失败", error=str(e))
                raise
            finally:
                db_manager.close_session(session)

        except Exception as e:
            self.logger.error(f"批量保存用户数据失败", error=str(e))

        return saved_count
    
    def _update_estimated_time(self):
        """更新预计完成时间"""
        if self.progress['processed'] > 0 and self.progress['start_time']:
            elapsed = (datetime.now() - self.progress['start_time']).total_seconds()
            rate = self.progress['processed'] / elapsed
            remaining = self.progress['total'] - self.progress['processed']
            
            if rate > 0:
                estimated_seconds = remaining / rate
                if estimated_seconds < 60:
                    self.progress['estimated_time'] = f"{int(estimated_seconds)}秒"
                elif estimated_seconds < 3600:
                    self.progress['estimated_time'] = f"{int(estimated_seconds/60)}分钟"
                else:
                    self.progress['estimated_time'] = f"{int(estimated_seconds/3600)}小时"
            else:
                self.progress['estimated_time'] = "计算中..."
    
    def pause(self):
        """暂停任务"""
        self.should_pause = True
        self.logger.info(f"任务暂停请求", task_id=self.task_id)
    
    def resume(self):
        """恢复任务"""
        self.should_pause = False
        self.logger.info(f"任务恢复请求", task_id=self.task_id)
    
    def stop(self):
        """停止任务"""
        self.should_stop = True
        self.logger.info(f"任务停止请求", task_id=self.task_id)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        """初始化任务管理器"""
        self.tasks: Dict[str, ExtractionTask] = {}
        self.logger = get_logger('task_manager')
    
    def create_task(self, session_id: str, uris: List[str]) -> str:
        """
        创建新任务
        
        Args:
            session_id: Instagram Session ID
            uris: URI列表
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        task = ExtractionTask(task_id, session_id, uris)
        self.tasks[task_id] = task
        
        self.logger.info(f"创建新任务", task_id=task_id, uri_count=len(uris))
        return task_id
    
    def start_task(self, task_id: str) -> bool:
        """
        启动任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否启动成功
        """
        task = self.tasks.get(task_id)
        if not task:
            self.logger.error(f"任务不存在", task_id=task_id)
            return False
        
        # 在后台线程中运行任务
        thread = threading.Thread(target=task.start, daemon=True)
        thread.start()
        
        self.logger.info(f"任务启动", task_id=task_id)
        return True
    
    def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务进度
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 进度信息
        """
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        progress = task.progress.copy()
        progress['status'] = task.status.value
        progress['task_id'] = task_id
        
        return progress
    
    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        task = self.tasks.get(task_id)
        if task:
            task.pause()
            return True
        return False
    
    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        task = self.tasks.get(task_id)
        if task:
            task.resume()
            return True
        return False
    
    def stop_task(self, task_id: str) -> bool:
        """停止任务"""
        task = self.tasks.get(task_id)
        if task:
            task.stop()
            return True
        return False
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        result = {}
        for task_id, task in self.tasks.items():
            result[task_id] = {
                'task_id': task_id,
                'status': task.status.value,
                'progress': task.progress
            }
        return result
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的旧任务"""
        current_time = datetime.now()
        to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.STOPPED] and
                task.progress.get('end_time')):
                
                age = (current_time - task.progress['end_time']).total_seconds() / 3600
                if age > max_age_hours:
                    to_remove.append(task_id)
        
        for task_id in to_remove:
            del self.tasks[task_id]
            self.logger.info(f"清理旧任务", task_id=task_id)


# 全局任务管理器实例
task_manager = TaskManager()
