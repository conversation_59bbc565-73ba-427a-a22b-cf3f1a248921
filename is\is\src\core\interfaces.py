"""
接口定义模块
定义系统中各模块的接口规范
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime


@dataclass
class UserData:
    """用户数据模型"""
    user_id: int
    username: str
    full_name: Optional[str] = None
    profile_pic_url: Optional[str] = None
    is_private: bool = False
    is_verified: bool = False
    follower_count: int = 0
    following_count: int = 0
    media_count: int = 0
    biography: Optional[str] = None
    external_url: Optional[str] = None
    is_business: bool = False
    comment_text: str = ""
    comment_time: Optional[datetime] = None
    post_url: str = ""
    extraction_time: Optional[datetime] = None


@dataclass
class AuthResult:
    """认证结果模型"""
    success: bool
    user_info: Optional[UserData] = None
    error_message: Optional[str] = None
    session_valid: bool = False


@dataclass
class ExtractionResult:
    """提取结果模型"""
    success: bool
    user_data: List[UserData]
    total_processed: int
    successful_count: int
    failed_count: int
    error_messages: List[str]
    processing_time: float = 0.0


@dataclass
class URIProcessResult:
    """URI处理结果模型"""
    success: bool
    valid_uris: List[str]
    invalid_uris: List[str]
    media_ids: List[str]
    error_messages: List[str]


@dataclass
class SaveResult:
    """保存结果模型"""
    success: bool
    saved_count: int
    updated_count: int
    duplicate_count: int
    error_count: int
    error_messages: List[str]


@dataclass
class ExportConfig:
    """导出配置模型"""
    format: str  # 'json', 'csv', 'excel'
    selected_fields: List[str]
    filter_conditions: Dict[str, Any]
    sort_options: Dict[str, str]
    output_path: str
    max_records: int = 10000


@dataclass
class StorageStats:
    """存储统计信息模型"""
    total_users: int
    total_posts: int
    total_comments: int
    database_size_mb: float
    last_update: datetime


class IAuthenticator(ABC):
    """身份验证接口"""
    
    @abstractmethod
    def authenticate(self, session_id: str, proxy_config: Dict[str, str] = None) -> AuthResult:
        """
        使用session ID进行身份验证
        
        Args:
            session_id: Instagram session ID
            proxy_config: 代理配置
            
        Returns:
            AuthResult: 认证结果对象
        """
        pass
    
    @abstractmethod
    def validate_session(self) -> bool:
        """
        验证当前会话是否有效
        
        Returns:
            bool: 会话是否有效
        """
        pass
    
    @abstractmethod
    def get_current_user(self) -> Optional[UserData]:
        """
        获取当前登录用户信息
        
        Returns:
            UserData: 用户信息，如果未登录则返回None
        """
        pass
    
    @abstractmethod
    def logout(self) -> bool:
        """
        登出当前会话
        
        Returns:
            bool: 是否成功登出
        """
        pass


class IURIProcessor(ABC):
    """URI处理接口"""
    
    @abstractmethod
    def process_uris(self, uri_list: List[str]) -> URIProcessResult:
        """
        处理URI列表，验证格式并提取媒体ID
        
        Args:
            uri_list: URI列表
            
        Returns:
            URIProcessResult: 处理结果
        """
        pass
    
    @abstractmethod
    def validate_uri(self, uri: str) -> bool:
        """
        验证单个URI格式
        
        Args:
            uri: Instagram URI
            
        Returns:
            bool: URI是否有效
        """
        pass
    
    @abstractmethod
    def extract_media_id(self, uri: str) -> Optional[str]:
        """
        从URI中提取媒体ID
        
        Args:
            uri: Instagram URI
            
        Returns:
            str: 媒体ID，如果提取失败则返回None
        """
        pass


class IDataExtractor(ABC):
    """数据提取接口"""
    
    @abstractmethod
    def extract_comments(self, media_ids: List[str], 
                        batch_size: int = 10,
                        progress_callback: callable = None) -> ExtractionResult:
        """
        提取评论和用户数据
        
        Args:
            media_ids: 媒体ID列表
            batch_size: 批处理大小
            progress_callback: 进度回调函数
            
        Returns:
            ExtractionResult: 提取结果对象
        """
        pass
    
    @abstractmethod
    def extract_user_info(self, user_id: int) -> Optional[UserData]:
        """
        提取单个用户的详细信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            UserData: 用户信息，如果提取失败则返回None
        """
        pass
    
    @abstractmethod
    def get_media_comments(self, media_id: str) -> List[Dict[str, Any]]:
        """
        获取指定媒体的所有评论
        
        Args:
            media_id: 媒体ID
            
        Returns:
            List[Dict]: 评论列表
        """
        pass


class IStorageManager(ABC):
    """存储管理接口"""
    
    @abstractmethod
    def save_users(self, user_data: List[UserData]) -> SaveResult:
        """
        保存用户数据到数据库
        
        Args:
            user_data: 用户数据列表
            
        Returns:
            SaveResult: 保存结果
        """
        pass
    
    @abstractmethod
    def export_data(self, export_config: ExportConfig) -> str:
        """
        导出数据到文件
        
        Args:
            export_config: 导出配置
            
        Returns:
            str: 导出文件路径
        """
        pass
    
    @abstractmethod
    def get_statistics(self) -> StorageStats:
        """
        获取存储统计信息
        
        Returns:
            StorageStats: 统计信息
        """
        pass
    
    @abstractmethod
    def search_users(self, filters: Dict[str, Any], 
                    limit: int = 100, offset: int = 0) -> List[UserData]:
        """
        搜索用户数据
        
        Args:
            filters: 搜索过滤条件
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            List[UserData]: 用户数据列表
        """
        pass
    
    @abstractmethod
    def delete_data(self, filters: Dict[str, Any]) -> int:
        """
        删除数据
        
        Args:
            filters: 删除条件
            
        Returns:
            int: 删除的记录数
        """
        pass


class ILogger(ABC):
    """日志记录接口"""
    
    @abstractmethod
    def debug(self, message: str, **kwargs):
        """记录调试信息"""
        pass
    
    @abstractmethod
    def info(self, message: str, **kwargs):
        """记录信息"""
        pass
    
    @abstractmethod
    def warning(self, message: str, **kwargs):
        """记录警告"""
        pass
    
    @abstractmethod
    def error(self, message: str, **kwargs):
        """记录错误"""
        pass
    
    @abstractmethod
    def critical(self, message: str, **kwargs):
        """记录严重错误"""
        pass


class IConfigManager(ABC):
    """配置管理接口"""
    
    @abstractmethod
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict: 配置字典
        """
        pass
    
    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键（支持点分隔的嵌套键）
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 是否设置成功
        """
        pass
    
    @abstractmethod
    def save_config(self, config_path: str = None) -> bool:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 是否保存成功
        """
        pass
