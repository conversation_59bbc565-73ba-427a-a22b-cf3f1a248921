# Instagram评论提取器配置文件

# 应用配置
app:
  name: "Instagram Comment Extractor"
  version: "1.0.0"
  debug: true
  host: "127.0.0.1"
  port: 5000
  secret_key: "your-secret-key-change-in-production"

# Instagram API配置
instagram:
  delay_range: [1, 3]  # 请求延迟范围（秒）
  batch_size: 10       # 批处理大小
  max_retries: 3       # 最大重试次数
  timeout: 30          # 请求超时时间
  user_agent: "Instagram **********.172 Android (26/8.0.0; 480dpi; 1080x1920; Xiaomi; MI 5s; capricorn; qcom; en_US; 301484483)"

# 数据库配置
database:
  type: "sqlite"
  path: "data/database/users.db"
  backup_enabled: true
  backup_interval: 3600  # 备份间隔（秒）

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "data/logs/app.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# 导出配置
export:
  default_format: "csv"
  max_records: 10000
  output_directory: "data/exports"
  available_formats: ["json", "csv", "excel"]
  
# 安全配置
security:
  session_encryption: true
  data_encryption: false
  max_session_age: 86400  # 24小时

# 代理配置（可选）
proxy:
  enabled: false
  http_proxy: ""
  https_proxy: ""
  socks_proxy: ""

# 性能配置
performance:
  max_concurrent_requests: 5
  memory_limit_mb: 512
  cache_enabled: true
  cache_ttl: 3600
