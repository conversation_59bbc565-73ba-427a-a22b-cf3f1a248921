# Instagram评论提取器

一个模块化、易扩展的Instagram评论用户账号保存系统，支持批量处理、数据存储和导出功能。

## 🚀 功能特性

- **身份验证**: 使用Instagram Session ID进行安全认证
- **批量处理**: 支持多个Instagram帖子链接的批量分析
- **数据提取**: 自动提取评论者的完整用户资料信息
- **数据存储**: SQLite数据库存储，支持数据去重和增量更新
- **数据导出**: 支持CSV、JSON、Excel多种格式导出
- **Web界面**: 直观的Web界面，支持实时进度显示
- **模块化设计**: 易于扩展和维护的架构

## 📋 系统要求

- Python 3.9+
- 现代Web浏览器（Chrome、Firefox、Safari、Edge）
- 至少500MB可用磁盘空间

## 🛠️ 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd instagram-comment-extractor
```

### 2. 创建虚拟环境
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 初始化数据库
```bash
python scripts/setup_database.py
```

### 5. 启动应用
```bash
python main.py
```

应用将在 http://127.0.0.1:5000 启动

## 📖 使用指南

### 1. 获取Instagram Session ID

1. 在浏览器中登录Instagram
2. 按F12打开开发者工具
3. 切换到"应用程序"或"Application"标签
4. 在左侧找到"Cookies" → "https://www.instagram.com"
5. 找到名为"sessionid"的cookie，复制其值

### 2. 使用系统

1. **输入Session ID**: 在首页输入获取的Session ID并测试连接
2. **添加帖子链接**: 输入要分析的Instagram帖子链接（支持批量输入）
3. **开始分析**: 点击"开始分析"按钮启动数据提取
4. **查看进度**: 实时查看提取进度和统计信息
5. **管理数据**: 在"数据管理"页面查看、筛选和导出数据

### 3. 支持的链接格式

```
https://www.instagram.com/p/DMcnC6ZTroi/
https://instagram.com/p/DMcnC6ZTroi/
https://www.instagram.com/p/DMcnC6ZTroi/?utm_source=...
```

## 🏗️ 项目架构

```
instagram_comment_extractor/
├── main.py                     # 应用入口点
├── requirements.txt            # 依赖包列表
├── config/                     # 配置模块
│   ├── config.yaml            # 配置文件
│   └── settings.py            # 配置管理
├── src/                       # 源代码
│   ├── auth/                  # 身份验证模块
│   ├── extractor/             # 数据提取模块
│   ├── storage/               # 数据存储模块
│   ├── ui/                    # 用户界面模块
│   ├── utils/                 # 工具模块
│   └── core/                  # 核心模块
├── tests/                     # 测试模块
├── docs/                      # 文档目录
├── data/                      # 数据目录
│   ├── database/              # 数据库文件
│   ├── exports/               # 导出文件
│   ├── logs/                  # 日志文件
│   └── sessions/              # 会话文件
└── scripts/                   # 脚本目录
```

## ⚙️ 配置说明

主要配置文件位于 `config/config.yaml`：

```yaml
# 应用配置
app:
  host: "127.0.0.1"
  port: 5000
  debug: false

# Instagram API配置
instagram:
  delay_range: [1, 3]  # 请求延迟范围（秒）
  batch_size: 10       # 批处理大小
  max_retries: 3       # 最大重试次数

# 数据库配置
database:
  type: "sqlite"
  path: "data/database/users.db"

# 日志配置
logging:
  level: "INFO"
  file_path: "data/logs/app.log"
```

## 📊 数据字段说明

系统提取的用户数据包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| user_id | 整数 | Instagram用户ID |
| username | 字符串 | 用户名 |
| full_name | 字符串 | 全名 |
| profile_pic_url | 字符串 | 头像URL |
| is_private | 布尔 | 是否私人账户 |
| is_verified | 布尔 | 是否认证账户 |
| is_business | 布尔 | 是否商业账户 |
| follower_count | 整数 | 粉丝数 |
| following_count | 整数 | 关注数 |
| media_count | 整数 | 帖子数 |
| biography | 字符串 | 个人简介 |
| external_url | 字符串 | 外部链接 |
| comment_text | 字符串 | 评论内容 |
| comment_time | 日期时间 | 评论时间 |
| post_url | 字符串 | 来源帖子URL |
| extraction_time | 日期时间 | 提取时间 |

## 🔧 命令行选项

```bash
python main.py --help

选项:
  --host HOST          服务器主机地址
  --port PORT          服务器端口
  --debug              启用调试模式
  --config CONFIG      配置文件路径
  --version            显示版本信息
```

## 📝 开发说明

### 运行测试
```bash
pytest tests/
```

### 代码格式化
```bash
black src/
```

### 类型检查
```bash
mypy src/
```

## ⚠️ 注意事项

1. **遵守使用条款**: 请遵守Instagram的使用条款和服务协议
2. **请求频率**: 建议使用适当的请求间隔，避免触发速率限制
3. **数据隐私**: 请妥善保管Session ID，不要分享给他人
4. **数据备份**: 建议定期备份重要数据
5. **网络环境**: 确保网络连接稳定，避免提取过程中断

## 🐛 故障排除

### 常见问题

**Q: Session ID验证失败**
A: 请检查Session ID是否正确，是否已过期，或尝试重新获取

**Q: 无法访问私人账户的评论**
A: 系统只能访问公开账户的评论，私人账户需要关注后才能访问

**Q: 提取速度很慢**
A: 这是正常现象，系统会自动添加延迟以避免触发Instagram的速率限制

**Q: 数据库文件损坏**
A: 可以删除 `data/database/users.db` 文件，然后重新运行 `python scripts/setup_database.py`

### 日志查看

应用日志位于 `data/logs/app.log`，可以查看详细的错误信息和运行状态。

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和平台使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发送邮件
- 查看文档

---

**免责声明**: 本工具仅供学习和研究目的使用，使用者需自行承担使用风险，并遵守Instagram的使用条款。
