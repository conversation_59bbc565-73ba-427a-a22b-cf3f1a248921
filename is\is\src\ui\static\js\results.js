/**
 * 结果页面JavaScript功能
 * 处理数据展示、筛选、搜索和导出功能
 */

class ResultsPageManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalPages = 1;
        this.totalRecords = 0;
        this.currentData = [];
        this.filters = {};
        this.sortBy = 'extraction_time';
        this.sortOrder = 'desc';
        
        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindEvents();
        this.loadStatistics();
        this.loadData();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 筛选和搜索事件
        document.getElementById('applyFilters').addEventListener('click', () => this.applyFilters());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyFilters();
            }
        });

        // 排序事件
        document.getElementById('sortBy').addEventListener('change', () => this.applyFilters());
        document.getElementById('sortOrder').addEventListener('change', () => this.applyFilters());

        // 分页事件
        document.getElementById('pageSize').addEventListener('change', () => {
            this.pageSize = parseInt(document.getElementById('pageSize').value);
            this.currentPage = 1;
            this.loadData();
        });
        document.getElementById('prevPage').addEventListener('click', (e) => {
            e.preventDefault();
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadData();
            }
        });
        document.getElementById('nextPage').addEventListener('click', (e) => {
            e.preventDefault();
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadData();
            }
        });

        // 全选事件
        document.getElementById('selectAll').addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('#dataTableBody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
            this.updateSelectedCount();
        });

        // 批量操作事件
        document.getElementById('deleteSelectedBtn').addEventListener('click', () => this.deleteSelected());
        document.getElementById('selectAllBtn').addEventListener('click', () => this.selectAll());
        document.getElementById('deselectAllBtn').addEventListener('click', () => this.deselectAll());

        // 导出事件
        document.querySelectorAll('.dropdown-item[data-format]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const format = e.target.closest('[data-format]').dataset.format;
                this.exportData(format);
            });
        });
    }

    /**
     * 加载统计信息
     */
    async loadStatistics() {
        try {
            const response = await ApiClient.get('/data/statistics');
            if (response.success) {
                const stats = response.stats;
                document.getElementById('totalUsersCount').textContent = stats.total_users || 0;
                document.getElementById('totalCommentsCount').textContent = stats.total_comments || 0;
                document.getElementById('totalPostsCount').textContent = stats.total_posts || 0;
                document.getElementById('verifiedUsersCount').textContent = stats.verified_users || 0;
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    /**
     * 加载数据
     */
    async loadData() {
        this.showLoading(true);
        
        try {
            // 构建查询参数
            const params = {
                page: this.currentPage,
                page_size: this.pageSize,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.filters
            };

            const response = await ApiClient.get('/data/list', params);
            
            if (response.success) {
                this.currentData = response.data || [];
                this.totalRecords = response.total || 0;
                this.totalPages = Math.ceil(this.totalRecords / this.pageSize);
                
                this.renderTable();
                this.updatePagination();
                this.updateRecordCount();
                
                if (this.currentData.length === 0) {
                    this.showEmptyState(true);
                } else {
                    this.showEmptyState(false);
                }
            } else {
                Utils.showMessage(response.message || '加载数据失败', 'error');
                this.showEmptyState(true);
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            Utils.showMessage('加载数据失败，请重试', 'error');
            this.showEmptyState(true);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 渲染表格
     */
    renderTable() {
        const tableBody = document.getElementById('dataTableBody');
        tableBody.innerHTML = '';

        this.currentData.forEach((user, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="form-check-input" value="${user.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        ${user.profile_pic_url ? 
                            `<img src="${user.profile_pic_url}" class="rounded-circle me-2" width="32" height="32" alt="头像">` : 
                            '<div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width:32px;height:32px;"><i class="fas fa-user text-white"></i></div>'
                        }
                        <div>
                            <strong>${user.username}</strong>
                            ${user.is_verified ? '<i class="fas fa-check-circle text-primary ms-1" title="已认证"></i>' : ''}
                        </div>
                    </div>
                </td>
                <td>${user.full_name || '--'}</td>
                <td>${Utils.formatNumber(user.follower_count || 0)}</td>
                <td>
                    ${user.is_verified ? 
                        '<span class="badge bg-primary">已认证</span>' : 
                        '<span class="badge bg-secondary">未认证</span>'
                    }
                </td>
                <td>
                    ${user.is_private ? 
                        '<span class="badge bg-warning">私人</span>' : 
                        '<span class="badge bg-success">公开</span>'
                    }
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${user.comment_text || ''}">
                        ${user.comment_text || '--'}
                    </div>
                </td>
                <td>${user.comment_time ? Utils.formatTime(user.comment_time) : '--'}</td>
                <td>${user.extraction_time ? Utils.formatTime(user.extraction_time) : '--'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="resultsPageManager.showUserDetail(${user.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="resultsPageManager.sendDM(${user.user_id})" title="发送私信">
                            <i class="fas fa-envelope"></i>
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="window.open('${user.post_url}', '_blank')" title="查看帖子">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);

            // 为复选框添加事件监听
            const checkbox = row.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', () => this.updateSelectedCount());
        });

        // 更新选中计数
        this.updateSelectedCount();
    }

    /**
     * 应用筛选
     */
    applyFilters() {
        this.filters = {};
        
        // 搜索关键词
        const searchInput = document.getElementById('searchInput').value.trim();
        if (searchInput) {
            this.filters.search = searchInput;
        }

        // 认证状态
        const filterVerified = document.getElementById('filterVerified').value;
        if (filterVerified) {
            this.filters.is_verified = filterVerified === 'true';
        }

        // 账户类型
        const filterPrivate = document.getElementById('filterPrivate').value;
        if (filterPrivate) {
            this.filters.is_private = filterPrivate === 'true';
        }

        // 排序
        this.sortBy = document.getElementById('sortBy').value;
        this.sortOrder = document.getElementById('sortOrder').value;

        // 重置到第一页
        this.currentPage = 1;
        
        // 重新加载数据
        this.loadData();
    }

    /**
     * 清空筛选
     */
    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('filterVerified').value = '';
        document.getElementById('filterPrivate').value = '';
        document.getElementById('sortBy').value = 'extraction_time';
        document.getElementById('sortOrder').value = 'desc';
        
        this.filters = {};
        this.sortBy = 'extraction_time';
        this.sortOrder = 'desc';
        this.currentPage = 1;
        
        this.loadData();
    }

    /**
     * 导出数据
     */
    async exportData(format) {
        try {
            const selectedIds = this.getSelectedIds();
            
            const exportData = {
                format: format,
                filters: this.filters,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                selected_ids: selectedIds.length > 0 ? selectedIds : null
            };

            Utils.showMessage('正在准备导出文件...', 'info');
            
            const response = await ApiClient.post('/data/export', exportData);
            
            if (response.success) {
                // 创建下载链接
                const link = document.createElement('a');
                link.href = response.download_url;
                link.download = response.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                Utils.showMessage('导出成功！', 'success');
            } else {
                Utils.showMessage(response.message || '导出失败', 'error');
            }
        } catch (error) {
            console.error('导出失败:', error);
            Utils.showMessage('导出失败，请重试', 'error');
        }
    }

    /**
     * 获取选中的ID列表
     */
    getSelectedIds() {
        const checkboxes = document.querySelectorAll('#dataTableBody input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(checkbox => parseInt(checkbox.value));
    }

    /**
     * 显示用户详情
     */
    async showUserDetail(userId) {
        try {
            const user = this.currentData.find(u => u.id === userId);
            if (!user) return;

            const modalContent = document.getElementById('userDetailContent');
            modalContent.innerHTML = `
                <div class="row">
                    <div class="col-md-4 text-center">
                        ${user.profile_pic_url ? 
                            `<img src="${user.profile_pic_url}" class="rounded-circle mb-3" width="120" height="120" alt="头像">` : 
                            '<div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width:120px;height:120px;"><i class="fas fa-user fa-3x text-white"></i></div>'
                        }
                        <h5>${user.username} ${user.is_verified ? '<i class="fas fa-check-circle text-primary"></i>' : ''}</h5>
                        <p class="text-muted">${user.full_name || '未设置全名'}</p>
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr><td><strong>用户ID:</strong></td><td>${user.user_id}</td></tr>
                            <tr><td><strong>粉丝数:</strong></td><td>${Utils.formatNumber(user.follower_count || 0)}</td></tr>
                            <tr><td><strong>关注数:</strong></td><td>${Utils.formatNumber(user.following_count || 0)}</td></tr>
                            <tr><td><strong>帖子数:</strong></td><td>${Utils.formatNumber(user.media_count || 0)}</td></tr>
                            <tr><td><strong>账户类型:</strong></td><td>${user.is_private ? '私人账户' : '公开账户'}</td></tr>
                            <tr><td><strong>商业账户:</strong></td><td>${user.is_business ? '是' : '否'}</td></tr>
                            <tr><td><strong>个人简介:</strong></td><td>${user.biography || '未设置'}</td></tr>
                            <tr><td><strong>外部链接:</strong></td><td>${user.external_url ? `<a href="${user.external_url}" target="_blank">${user.external_url}</a>` : '未设置'}</td></tr>
                            <tr><td><strong>评论内容:</strong></td><td>${user.comment_text || '--'}</td></tr>
                            <tr><td><strong>评论时间:</strong></td><td>${user.comment_time ? Utils.formatTime(user.comment_time) : '--'}</td></tr>
                            <tr><td><strong>来源帖子:</strong></td><td><a href="${user.post_url}" target="_blank">查看帖子</a></td></tr>
                        </table>
                    </div>
                </div>
            `;

            // 设置发送私信按钮的用户ID
            document.getElementById('sendDMButton').onclick = () => this.sendDM(user.user_id);

            // 显示模态框
            new bootstrap.Modal(document.getElementById('userDetailModal')).show();
        } catch (error) {
            console.error('显示用户详情失败:', error);
            Utils.showMessage('加载用户详情失败', 'error');
        }
    }

    /**
     * 发送私信
     */
    sendDM(userId) {
        // 这里应该实现发送私信的功能
        Utils.showMessage('私信功能开发中...', 'info');
    }

    /**
     * 更新分页信息
     */
    updatePagination() {
        const pageInfo = document.getElementById('pageInfo');
        pageInfo.textContent = `第 ${this.currentPage} 页，共 ${this.totalPages} 页`;

        const prevPageItem = document.getElementById('prevPageItem');
        const nextPageItem = document.getElementById('nextPageItem');

        if (this.currentPage <= 1) {
            prevPageItem.classList.add('disabled');
        } else {
            prevPageItem.classList.remove('disabled');
        }

        if (this.currentPage >= this.totalPages) {
            nextPageItem.classList.add('disabled');
        } else {
            nextPageItem.classList.remove('disabled');
        }
    }

    /**
     * 更新记录数量显示
     */
    updateRecordCount() {
        const totalRecords = document.getElementById('totalRecords');
        totalRecords.textContent = `${this.totalRecords} 条记录`;
    }

    /**
     * 显示/隐藏加载状态
     */
    showLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const dataTable = document.getElementById('dataTable');
        
        if (show) {
            loadingState.style.display = 'block';
            dataTable.style.display = 'none';
        } else {
            loadingState.style.display = 'none';
            dataTable.style.display = 'table';
        }
    }

    /**
     * 显示/隐藏空状态
     */
    showEmptyState(show) {
        const emptyState = document.getElementById('emptyState');
        const dataTable = document.getElementById('dataTable');

        if (show) {
            emptyState.style.display = 'block';
            dataTable.style.display = 'none';
        } else {
            emptyState.style.display = 'none';
            dataTable.style.display = 'table';
        }
    }

    /**
     * 更新选中计数
     */
    updateSelectedCount() {
        const checkboxes = document.querySelectorAll('#dataTableBody input[type="checkbox"]:checked');
        const count = checkboxes.length;

        document.getElementById('selectedCount').textContent = count;
        document.getElementById('deleteSelectedBtn').disabled = count === 0;

        // 更新全选状态
        const allCheckboxes = document.querySelectorAll('#dataTableBody input[type="checkbox"]');
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allCheckboxes.length > 0 && count === allCheckboxes.length;
            selectAllCheckbox.indeterminate = count > 0 && count < allCheckboxes.length;
        }
    }

    /**
     * 全选
     */
    selectAll() {
        const checkboxes = document.querySelectorAll('#dataTableBody input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = true);
        this.updateSelectedCount();
    }

    /**
     * 取消全选
     */
    deselectAll() {
        const checkboxes = document.querySelectorAll('#dataTableBody input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
        this.updateSelectedCount();
    }

    /**
     * 删除选中的用户
     */
    async deleteSelected() {
        const checkboxes = document.querySelectorAll('#dataTableBody input[type="checkbox"]:checked');
        const userIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

        if (userIds.length === 0) {
            Utils.showAlert('请选择要删除的用户', 'warning');
            return;
        }

        // 确认删除
        if (!confirm(`确定要删除选中的 ${userIds.length} 个用户吗？此操作不可撤销。`)) {
            return;
        }

        try {
            // 显示加载状态
            const deleteBtn = document.getElementById('deleteSelectedBtn');
            const originalText = deleteBtn.innerHTML;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            deleteBtn.disabled = true;

            const response = await fetch('/api/data/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_ids: userIds
                })
            });

            const result = await response.json();

            if (result.success) {
                Utils.showAlert(`成功删除 ${result.deleted_count} 条用户数据`, 'success');

                // 刷新数据
                await this.loadData();

                // 重置选中状态
                this.updateSelectedCount();
            } else {
                Utils.showAlert(result.message || '删除失败', 'error');
            }

        } catch (error) {
            console.error('删除用户数据失败:', error);
            Utils.showAlert('删除失败，请重试', 'error');
        } finally {
            // 恢复按钮状态
            const deleteBtn = document.getElementById('deleteSelectedBtn');
            deleteBtn.innerHTML = originalText;
            this.updateSelectedCount(); // 这会重新设置disabled状态
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.resultsPageManager = new ResultsPageManager();
});
