# 项目架构设计文档

## 1. 项目结构

```
instagram_comment_extractor/
├── main.py                     # 应用入口点
├── requirements.txt            # 依赖包列表
├── config/
│   ├── __init__.py
│   ├── settings.py            # 配置管理
│   └── config.yaml            # 配置文件
├── src/
│   ├── __init__.py
│   ├── auth/                  # 身份验证模块
│   │   ├── __init__.py
│   │   ├── authenticator.py   # 认证核心逻辑
│   │   └── session_manager.py # 会话管理
│   ├── extractor/             # 数据提取模块
│   │   ├── __init__.py
│   │   ├── comment_extractor.py  # 评论提取
│   │   ├── user_extractor.py     # 用户信息提取
│   │   └── uri_processor.py      # URI处理
│   ├── storage/               # 数据存储模块
│   │   ├── __init__.py
│   │   ├── database.py        # 数据库操作
│   │   ├── models.py          # 数据模型
│   │   └── export_manager.py  # 导出管理
│   ├── ui/                    # 用户界面模块
│   │   ├── __init__.py
│   │   ├── web_app.py         # Flask Web应用
│   │   ├── static/            # 静态文件
│   │   │   ├── css/
│   │   │   ├── js/
│   │   │   └── images/
│   │   └── templates/         # HTML模板
│   │       ├── index.html
│   │       ├── results.html
│   │       └── base.html
│   ├── utils/                 # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py          # 日志管理
│   │   ├── validators.py      # 数据验证
│   │   └── helpers.py         # 辅助函数
│   └── core/                  # 核心模块
│       ├── __init__.py
│       ├── exceptions.py      # 自定义异常
│       ├── constants.py       # 常量定义
│       └── interfaces.py      # 接口定义
├── tests/                     # 测试模块
│   ├── __init__.py
│   ├── test_auth.py
│   ├── test_extractor.py
│   ├── test_storage.py
│   └── test_ui.py
├── docs/                      # 文档目录
│   ├── instagram_api_analysis.md
│   ├── core_requirements.md
│   ├── project_architecture.md
│   └── user_guide.md
├── data/                      # 数据目录
│   ├── database/              # 数据库文件
│   ├── exports/               # 导出文件
│   ├── logs/                  # 日志文件
│   └── sessions/              # 会话文件
└── scripts/                   # 脚本目录
    ├── setup_database.py     # 数据库初始化
    └── cleanup.py             # 清理脚本
```

## 2. 核心架构设计

### 2.1 分层架构
```
┌─────────────────────────────────────┐
│           表示层 (UI Layer)          │
│         Flask Web Interface        │
├─────────────────────────────────────┤
│          业务逻辑层 (Service)        │
│    Auth | Extractor | Storage      │
├─────────────────────────────────────┤
│          数据访问层 (Data)           │
│      Database | File System        │
├─────────────────────────────────────┤
│          外部服务层 (External)       │
│        Instagram API | Proxy       │
└─────────────────────────────────────┘
```

### 2.2 模块间依赖关系
```
UI Module
    ↓
Core Service (Orchestrator)
    ↓
┌─────────────┬─────────────┬─────────────┐
│ Auth Module │ Extract Mod │ Storage Mod │
└─────────────┴─────────────┴─────────────┘
    ↓              ↓              ↓
┌─────────────┬─────────────┬─────────────┐
│ Session Mgr │ Instagram   │ Database    │
│             │ API Client  │ Manager     │
└─────────────┴─────────────┴─────────────┘
```

## 3. 接口定义

### 3.1 认证模块接口
```python
class IAuthenticator:
    """身份验证接口"""
    
    def authenticate(self, session_id: str, proxy_config: dict = None) -> AuthResult:
        """
        使用session ID进行身份验证
        
        Args:
            session_id: Instagram session ID
            proxy_config: 代理配置
            
        Returns:
            AuthResult: 认证结果对象
        """
        pass
    
    def validate_session(self) -> bool:
        """验证当前会话是否有效"""
        pass
    
    def get_current_user(self) -> UserInfo:
        """获取当前登录用户信息"""
        pass
```

### 3.2 数据提取模块接口
```python
class IDataExtractor:
    """数据提取接口"""
    
    def extract_comments(self, media_ids: List[str], 
                        batch_size: int = 10) -> ExtractionResult:
        """
        提取评论和用户数据
        
        Args:
            media_ids: 媒体ID列表
            batch_size: 批处理大小
            
        Returns:
            ExtractionResult: 提取结果对象
        """
        pass
    
    def process_uris(self, uri_list: List[str]) -> URIProcessResult:
        """处理URI列表"""
        pass
```

### 3.3 存储模块接口
```python
class IStorageManager:
    """存储管理接口"""
    
    def save_users(self, user_data: List[UserData]) -> SaveResult:
        """保存用户数据"""
        pass
    
    def export_data(self, export_config: ExportConfig) -> str:
        """导出数据"""
        pass
    
    def get_statistics(self) -> StorageStats:
        """获取存储统计信息"""
        pass
```

## 4. 数据模型定义

### 4.1 核心数据模型
```python
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List

@dataclass
class UserData:
    """用户数据模型"""
    user_id: int
    username: str
    full_name: Optional[str] = None
    profile_pic_url: Optional[str] = None
    is_private: bool = False
    is_verified: bool = False
    follower_count: int = 0
    following_count: int = 0
    media_count: int = 0
    biography: Optional[str] = None
    external_url: Optional[str] = None
    is_business: bool = False
    comment_text: str = ""
    comment_time: Optional[datetime] = None
    post_url: str = ""
    extraction_time: datetime = None

@dataclass
class AuthResult:
    """认证结果模型"""
    success: bool
    user_info: Optional[UserData] = None
    error_message: Optional[str] = None
    session_valid: bool = False

@dataclass
class ExtractionResult:
    """提取结果模型"""
    success: bool
    user_data: List[UserData]
    total_processed: int
    successful_count: int
    failed_count: int
    error_messages: List[str]

@dataclass
class ExportConfig:
    """导出配置模型"""
    format: str  # 'json', 'csv', 'excel'
    selected_fields: List[str]
    filter_conditions: dict
    sort_options: dict
    output_path: str
```

## 5. 配置管理

### 5.1 配置文件结构 (config.yaml)
```yaml
# 应用配置
app:
  name: "Instagram Comment Extractor"
  version: "1.0.0"
  debug: false
  host: "127.0.0.1"
  port: 5000

# Instagram API配置
instagram:
  delay_range: [1, 3]  # 请求延迟范围（秒）
  batch_size: 10       # 批处理大小
  max_retries: 3       # 最大重试次数
  timeout: 30          # 请求超时时间

# 数据库配置
database:
  type: "sqlite"
  path: "data/database/users.db"
  backup_enabled: true
  backup_interval: 3600  # 备份间隔（秒）

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "data/logs/app.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# 导出配置
export:
  default_format: "csv"
  max_records: 10000
  output_directory: "data/exports"
  
# 安全配置
security:
  session_encryption: true
  data_encryption: false
  max_session_age: 86400  # 24小时
```

## 6. 错误处理策略

### 6.1 异常层次结构
```python
class InstagramExtractorError(Exception):
    """基础异常类"""
    pass

class AuthenticationError(InstagramExtractorError):
    """认证相关异常"""
    pass

class ExtractionError(InstagramExtractorError):
    """数据提取异常"""
    pass

class StorageError(InstagramExtractorError):
    """存储相关异常"""
    pass

class ValidationError(InstagramExtractorError):
    """数据验证异常"""
    pass
```

### 6.2 全局错误处理器
```python
class ErrorHandler:
    """全局错误处理器"""
    
    def handle_exception(self, exc: Exception) -> ErrorResponse:
        """处理异常并返回用户友好的错误信息"""
        pass
    
    def log_error(self, exc: Exception, context: dict):
        """记录错误日志"""
        pass
    
    def should_retry(self, exc: Exception) -> bool:
        """判断是否应该重试"""
        pass
```

## 7. 扩展点设计

### 7.1 插件架构
```python
class IPlugin:
    """插件接口"""
    
    def get_name(self) -> str:
        """获取插件名称"""
        pass
    
    def initialize(self, config: dict):
        """初始化插件"""
        pass
    
    def process_data(self, data: any) -> any:
        """处理数据"""
        pass

class PluginManager:
    """插件管理器"""
    
    def register_plugin(self, plugin: IPlugin):
        """注册插件"""
        pass
    
    def execute_plugins(self, hook_name: str, data: any):
        """执行插件"""
        pass
```

### 7.2 扩展点
- **数据处理扩展**: 支持自定义数据处理逻辑
- **导出格式扩展**: 支持新的导出格式
- **认证方式扩展**: 支持新的认证方式
- **存储后端扩展**: 支持不同的存储后端

## 8. 性能优化策略

### 8.1 数据库优化
- 索引优化：user_id, post_url, extraction_time
- 连接池管理
- 批量插入操作
- 定期数据清理

### 8.2 内存管理
- 流式数据处理
- 对象池复用
- 及时释放资源
- 内存使用监控

### 8.3 并发处理
- 异步I/O操作
- 线程池管理
- 队列缓冲
- 限流控制

## 9. 安全考虑

### 9.1 数据安全
- Session ID加密存储
- 敏感数据脱敏
- 访问权限控制
- 数据传输加密

### 9.2 API安全
- 请求频率限制
- 代理轮换
- 用户代理随机化
- 异常检测和恢复

## 10. 部署架构

### 10.1 单机部署
```
┌─────────────────────────────────┐
│         Web Browser             │
└─────────────┬───────────────────┘
              │ HTTP
┌─────────────▼───────────────────┐
│      Flask Web Server           │
│  ┌─────────┬─────────┬────────┐ │
│  │   UI    │ Service │ Storage│ │
│  └─────────┴─────────┴────────┘ │
└─────────────┬───────────────────┘
              │
┌─────────────▼───────────────────┐
│        SQLite Database          │
└─────────────────────────────────┘
```

### 10.2 扩展部署（未来）
- 微服务架构
- 容器化部署
- 负载均衡
- 分布式存储
