"""
数据模型模块
定义数据库表结构和ORM模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, Text, 
    create_engine, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func

from src.core.interfaces import UserData
from src.core.constants import DATABASE_PATH

# 创建基础模型类
Base = declarative_base()


class User(Base):
    """用户数据模型"""
    
    __tablename__ = 'users'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Instagram用户信息
    user_id = Column(Integer, nullable=False, comment='Instagram用户ID')
    username = Column(String(255), nullable=False, comment='用户名')
    full_name = Column(String(255), nullable=True, comment='全名')
    profile_pic_url = Column(Text, nullable=True, comment='头像URL')
    
    # 账户状态
    is_private = Column(Boolean, default=False, comment='是否私人账户')
    is_verified = Column(Boolean, default=False, comment='是否认证账户')
    is_business = Column(Boolean, default=False, comment='是否商业账户')
    
    # 统计信息
    follower_count = Column(Integer, default=0, comment='粉丝数')
    following_count = Column(Integer, default=0, comment='关注数')
    media_count = Column(Integer, default=0, comment='帖子数')
    
    # 个人信息
    biography = Column(Text, nullable=True, comment='个人简介')
    external_url = Column(Text, nullable=True, comment='外部链接')
    
    # 评论信息
    comment_text = Column(Text, nullable=False, comment='评论内容')
    comment_time = Column(DateTime, nullable=True, comment='评论时间')
    post_url = Column(String(500), nullable=False, comment='帖子URL')
    
    # 系统信息
    extraction_time = Column(DateTime, default=func.now(), comment='提取时间')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 创建唯一约束：同一用户在同一帖子下只能有一条记录
    __table_args__ = (
        UniqueConstraint('user_id', 'post_url', name='uq_user_post'),
        Index('idx_user_id', 'user_id'),
        Index('idx_username', 'username'),
        Index('idx_post_url', 'post_url'),
        Index('idx_extraction_time', 'extraction_time'),
        Index('idx_is_verified', 'is_verified'),
        Index('idx_follower_count', 'follower_count'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 字典格式的用户数据
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.username,
            'full_name': self.full_name,
            'profile_pic_url': self.profile_pic_url,
            'is_private': self.is_private,
            'is_verified': self.is_verified,
            'is_business': self.is_business,
            'follower_count': self.follower_count,
            'following_count': self.following_count,
            'media_count': self.media_count,
            'biography': self.biography,
            'external_url': self.external_url,
            'comment_text': self.comment_text,
            'comment_time': self.comment_time.isoformat() if self.comment_time else None,
            'post_url': self.post_url,
            'extraction_time': self.extraction_time.isoformat() if self.extraction_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def to_user_data(self) -> UserData:
        """
        转换为UserData对象
        
        Returns:
            UserData: UserData对象
        """
        return UserData(
            user_id=self.user_id,
            username=self.username,
            full_name=self.full_name,
            profile_pic_url=self.profile_pic_url,
            is_private=self.is_private,
            is_verified=self.is_verified,
            follower_count=self.follower_count,
            following_count=self.following_count,
            media_count=self.media_count,
            biography=self.biography,
            external_url=self.external_url,
            is_business=self.is_business,
            comment_text=self.comment_text,
            comment_time=self.comment_time,
            post_url=self.post_url,
            extraction_time=self.extraction_time
        )
    
    @classmethod
    def from_user_data(cls, user_data: UserData) -> 'User':
        """
        从UserData对象创建User实例
        
        Args:
            user_data: UserData对象
            
        Returns:
            User: User实例
        """
        return cls(
            user_id=user_data.user_id,
            username=user_data.username,
            full_name=user_data.full_name,
            profile_pic_url=user_data.profile_pic_url,
            is_private=user_data.is_private,
            is_verified=user_data.is_verified,
            follower_count=user_data.follower_count,
            following_count=user_data.following_count,
            media_count=user_data.media_count,
            biography=user_data.biography,
            external_url=user_data.external_url,
            is_business=user_data.is_business,
            comment_text=user_data.comment_text,
            comment_time=user_data.comment_time,
            post_url=user_data.post_url,
            extraction_time=user_data.extraction_time or datetime.now()
        )
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, username='{self.username}', user_id={self.user_id})>"


class ExtractionLog(Base):
    """数据提取日志模型"""
    
    __tablename__ = 'extraction_logs'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 提取任务信息
    session_id_hash = Column(String(64), nullable=False, comment='Session ID哈希值')
    post_urls = Column(Text, nullable=False, comment='帖子URL列表（JSON格式）')
    
    # 提取结果统计
    total_posts = Column(Integer, default=0, comment='总帖子数')
    total_comments = Column(Integer, default=0, comment='总评论数')
    successful_users = Column(Integer, default=0, comment='成功提取用户数')
    failed_users = Column(Integer, default=0, comment='失败用户数')
    
    # 时间信息
    start_time = Column(DateTime, nullable=False, comment='开始时间')
    end_time = Column(DateTime, nullable=True, comment='结束时间')
    duration_seconds = Column(Integer, nullable=True, comment='持续时间（秒）')
    
    # 状态信息
    status = Column(String(50), default='running', comment='状态：running, completed, failed, stopped')
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 系统信息
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 创建索引
    __table_args__ = (
        Index('idx_session_hash', 'session_id_hash'),
        Index('idx_start_time', 'start_time'),
        Index('idx_status', 'status'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 字典格式的日志数据
        """
        return {
            'id': self.id,
            'session_id_hash': self.session_id_hash,
            'post_urls': self.post_urls,
            'total_posts': self.total_posts,
            'total_comments': self.total_comments,
            'successful_users': self.successful_users,
            'failed_users': self.failed_users,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration_seconds': self.duration_seconds,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self) -> str:
        return f"<ExtractionLog(id={self.id}, status='{self.status}', total_posts={self.total_posts})>"


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str = None):
        """
        初始化数据库管理器
        
        Args:
            database_url: 数据库连接URL
        """
        if database_url is None:
            database_url = f"sqlite:///{DATABASE_PATH}"
        
        self.engine = create_engine(
            database_url,
            echo=False,  # 设置为True可以看到SQL语句
            pool_pre_ping=True,  # 连接前检查连接是否有效
            connect_args={"check_same_thread": False} if "sqlite" in database_url else {}
        )
        
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 创建表
        self.create_tables()
    
    def create_tables(self):
        """创建数据库表"""
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self) -> Session:
        """
        获取数据库会话
        
        Returns:
            Session: 数据库会话对象
        """
        return self.SessionLocal()
    
    def close_session(self, session: Session):
        """
        关闭数据库会话
        
        Args:
            session: 数据库会话对象
        """
        session.close()
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        session = self.get_session()
        try:
            # 用户统计
            total_users = session.query(User).count()
            unique_users = session.query(User.user_id).distinct().count()
            total_comments = session.query(User).count()  # 每条记录代表一个评论
            
            # 帖子统计
            unique_posts = session.query(User.post_url).distinct().count()
            
            # 认证用户统计
            verified_users = session.query(User).filter(User.is_verified == True).count()
            
            # 最近提取时间
            latest_extraction = session.query(func.max(User.extraction_time)).scalar()
            
            return {
                'total_records': total_users,
                'unique_users': unique_users,
                'total_comments': total_comments,
                'unique_posts': unique_posts,
                'verified_users': verified_users,
                'latest_extraction': latest_extraction.isoformat() if latest_extraction else None,
                'database_size_mb': self._get_database_size()
            }
        finally:
            self.close_session(session)
    
    def _get_database_size(self) -> float:
        """
        获取数据库文件大小（MB）
        
        Returns:
            float: 数据库大小（MB）
        """
        try:
            import os
            if "sqlite" in str(self.engine.url):
                db_path = str(self.engine.url).replace("sqlite:///", "")
                if os.path.exists(db_path):
                    size_bytes = os.path.getsize(db_path)
                    return size_bytes / 1024 / 1024
            return 0.0
        except Exception:
            return 0.0
    
    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            bool: 是否备份成功
        """
        try:
            if "sqlite" in str(self.engine.url):
                import shutil
                db_path = str(self.engine.url).replace("sqlite:///", "")
                shutil.copy2(db_path, backup_path)
                return True
            return False
        except Exception:
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()
