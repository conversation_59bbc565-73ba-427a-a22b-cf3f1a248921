# 用户界面设计文档

## 1. 界面概述

设计一个直观、用户友好的Web界面，支持Instagram评论数据提取的完整工作流程。界面采用响应式设计，支持桌面和移动设备访问。

## 2. 页面结构

### 2.1 主页面 (index.html)
**功能**: 数据提取的主要操作界面

**布局结构**:
```
┌─────────────────────────────────────────┐
│              页面标题                    │
├─────────────────────────────────────────┤
│          Session ID 输入区域             │
├─────────────────────────────────────────┤
│           URI 批量输入区域               │
├─────────────────────────────────────────┤
│          操作按钮区域                    │
├─────────────────────────────────────────┤
│          进度显示区域                    │
├─────────────────────────────────────────┤
│          结果展示区域                    │
└─────────────────────────────────────────┘
```

### 2.2 结果页面 (results.html)
**功能**: 详细展示提取结果和数据管理

**布局结构**:
```
┌─────────────────────────────────────────┐
│          统计信息面板                    │
├─────────────────────────────────────────┤
│          筛选和搜索工具栏                │
├─────────────────────────────────────────┤
│          数据表格展示区域                │
├─────────────────────────────────────────┤
│          分页和导出控制                  │
└─────────────────────────────────────────┘
```

## 3. 详细界面设计

### 3.1 Session ID 输入区域
**组件**:
- 文本输入框（支持粘贴）
- 验证状态指示器
- 帮助提示按钮
- 测试连接按钮

**功能**:
- 实时格式验证
- 安全存储（本地加密）
- 连接状态检测
- 错误提示显示

**HTML结构**:
```html
<div class="session-input-section">
    <h3>Instagram Session ID</h3>
    <div class="input-group">
        <input type="password" id="sessionId" placeholder="请输入Instagram Session ID">
        <button type="button" id="testConnection">测试连接</button>
        <button type="button" id="helpSession">?</button>
    </div>
    <div class="status-indicator" id="sessionStatus">
        <span class="status-text">未连接</span>
    </div>
</div>
```

### 3.2 URI 批量输入区域
**组件**:
- 大文本输入框
- URI格式示例
- 批量粘贴支持
- 格式验证提示

**功能**:
- 支持多种分隔符（换行、逗号、分号）
- 实时URI数量统计
- 格式验证和错误提示
- 去重处理

**HTML结构**:
```html
<div class="uri-input-section">
    <h3>Instagram 帖子链接</h3>
    <div class="input-help">
        <p>支持格式：https://www.instagram.com/p/DMcnC6ZTroi/</p>
        <p>多个链接请用换行或逗号分隔</p>
    </div>
    <textarea id="uriInput" rows="8" placeholder="请输入Instagram帖子链接..."></textarea>
    <div class="uri-stats">
        <span>有效链接: <span id="validCount">0</span></span>
        <span>无效链接: <span id="invalidCount">0</span></span>
    </div>
</div>
```

### 3.3 操作按钮区域
**组件**:
- 开始分析按钮
- 暂停/继续按钮
- 停止按钮
- 清空数据按钮

**功能**:
- 状态感知（启用/禁用）
- 操作确认对话框
- 快捷键支持

**HTML结构**:
```html
<div class="action-buttons">
    <button type="button" id="startAnalysis" class="btn-primary">开始分析</button>
    <button type="button" id="pauseAnalysis" class="btn-secondary" disabled>暂停</button>
    <button type="button" id="stopAnalysis" class="btn-danger" disabled>停止</button>
    <button type="button" id="clearData" class="btn-warning">清空数据</button>
</div>
```

### 3.4 进度显示区域
**组件**:
- 总体进度条
- 当前操作状态
- 实时统计信息
- 错误日志显示

**功能**:
- 实时进度更新
- 详细状态信息
- 错误信息展示
- 预计完成时间

**HTML结构**:
```html
<div class="progress-section" id="progressSection" style="display: none;">
    <h3>提取进度</h3>
    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
        <span class="progress-text" id="progressText">0%</span>
    </div>
    <div class="progress-stats">
        <div class="stat-item">
            <span class="stat-label">已处理:</span>
            <span class="stat-value" id="processedCount">0</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">成功:</span>
            <span class="stat-value" id="successCount">0</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">失败:</span>
            <span class="stat-value" id="failedCount">0</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">预计完成:</span>
            <span class="stat-value" id="estimatedTime">--</span>
        </div>
    </div>
    <div class="current-status" id="currentStatus">准备开始...</div>
</div>
```

### 3.5 结果展示区域
**组件**:
- 数据表格
- 排序控制
- 筛选工具
- 导出选项

**功能**:
- 分页显示
- 多列排序
- 实时搜索
- 批量选择

**HTML结构**:
```html
<div class="results-section" id="resultsSection" style="display: none;">
    <h3>提取结果</h3>
    <div class="results-toolbar">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索用户名、评论内容...">
        </div>
        <div class="filter-controls">
            <select id="filterVerified">
                <option value="">所有用户</option>
                <option value="true">已认证</option>
                <option value="false">未认证</option>
            </select>
            <select id="filterPrivate">
                <option value="">所有账户</option>
                <option value="true">私人账户</option>
                <option value="false">公开账户</option>
            </select>
        </div>
        <div class="export-controls">
            <select id="exportFormat">
                <option value="csv">CSV格式</option>
                <option value="json">JSON格式</option>
                <option value="excel">Excel格式</option>
            </select>
            <button type="button" id="exportData" class="btn-success">导出数据</button>
        </div>
    </div>
    <div class="table-container">
        <table id="resultsTable" class="data-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="selectAll"></th>
                    <th data-sort="username">用户名 ↕</th>
                    <th data-sort="full_name">全名 ↕</th>
                    <th data-sort="follower_count">粉丝数 ↕</th>
                    <th data-sort="is_verified">认证状态 ↕</th>
                    <th data-sort="comment_text">评论内容 ↕</th>
                    <th data-sort="comment_time">评论时间 ↕</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="resultsTableBody">
                <!-- 动态生成的数据行 -->
            </tbody>
        </table>
    </div>
    <div class="pagination-controls">
        <button type="button" id="prevPage" disabled>上一页</button>
        <span id="pageInfo">第 1 页，共 1 页</span>
        <button type="button" id="nextPage" disabled>下一页</button>
        <select id="pageSize">
            <option value="20">20条/页</option>
            <option value="50">50条/页</option>
            <option value="100">100条/页</option>
        </select>
    </div>
</div>
```

## 4. 交互流程设计

### 4.1 基本操作流程
```
用户输入Session ID → 验证连接 → 输入URI列表 → 验证URI格式 → 
点击开始分析 → 显示进度 → 实时更新状态 → 完成后显示结果 → 
用户查看/筛选数据 → 选择导出格式 → 下载文件
```

### 4.2 错误处理流程
```
检测到错误 → 显示错误提示 → 提供解决建议 → 
用户确认 → 继续/重试/停止操作
```

### 4.3 进度更新机制
- WebSocket实时通信
- 每处理10个用户更新一次进度
- 显示当前处理的帖子URL
- 错误信息实时显示

## 5. 响应式设计

### 5.1 桌面端 (>1024px)
- 三列布局
- 侧边栏显示统计信息
- 大表格展示数据

### 5.2 平板端 (768px-1024px)
- 两列布局
- 折叠侧边栏
- 简化表格列

### 5.3 移动端 (<768px)
- 单列布局
- 卡片式数据展示
- 触摸友好的控件

## 6. 用户体验优化

### 6.1 加载状态
- 骨架屏加载效果
- 进度指示器
- 操作反馈动画

### 6.2 错误提示
- 友好的错误消息
- 具体的解决建议
- 一键重试功能

### 6.3 数据可视化
- 统计图表
- 进度可视化
- 数据分布图

### 6.4 快捷操作
- 键盘快捷键
- 批量操作
- 智能建议

## 7. 技术实现

### 7.1 前端技术栈
- HTML5 + CSS3
- JavaScript (ES6+)
- Bootstrap 5 (响应式框架)
- Chart.js (图表库)
- WebSocket (实时通信)

### 7.2 后端接口
- RESTful API设计
- JSON数据格式
- WebSocket事件推送
- 文件下载接口

### 7.3 状态管理
- 本地存储Session ID
- 操作历史记录
- 用户偏好设置

## 8. 安全考虑

### 8.1 数据保护
- Session ID加密存储
- HTTPS传输
- 敏感信息脱敏显示

### 8.2 输入验证
- 前端格式验证
- 后端安全检查
- XSS防护

### 8.3 访问控制
- 会话超时处理
- 操作权限验证
- 审计日志记录
