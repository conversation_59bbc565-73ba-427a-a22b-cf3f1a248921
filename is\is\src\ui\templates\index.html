{% extends "base.html" %}

{% block title %}首页 - Instagram评论提取器{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧主要操作区域 -->
    <div class="col-lg-8">
        <!-- Session ID 输入区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>Instagram 身份验证
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="sessionId" class="form-label">Session ID</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="sessionId"
                               placeholder="75995718761%3AViYvzu2bzc1AnC%3A12%3AAYesnOHlWUI2FjpGK4GMGb8bM_llxyWUuzPGEvVeTA">
                        <button class="btn btn-outline-primary" type="button" id="testConnection">
                            <i class="fas fa-plug me-1"></i>测试连接
                        </button>
                        <button class="btn btn-outline-info" type="button" data-bs-toggle="modal" data-bs-target="#helpModal">
                            <i class="fas fa-question-circle"></i>
                        </button>
                    </div>
                    <div class="form-text">
                        Session ID将被安全存储，用于访问Instagram API
                    </div>
                </div>
                <div class="alert alert-secondary" role="alert" id="sessionStatus">
                    <i class="fas fa-circle text-secondary me-2"></i>
                    <span class="status-text">未连接</span>
                </div>
            </div>
        </div>

        <!-- URI 批量输入区域 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>Instagram 帖子链接
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="uriInput" class="form-label">帖子链接列表</label>
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            支持格式：https://www.instagram.com/p/DMcnC6ZTroi/ | 多个链接请用换行或逗号分隔
                        </small>
                    </div>
                    <textarea class="form-control" id="uriInput" rows="8"
                              placeholder="https://www.instagram.com/p/DMcnC6ZTroi/&#10;请输入Instagram帖子链接，每行一个或用逗号分隔..."></textarea>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">有效链接: <span id="validCount">0</span></span>
                            <span class="badge bg-danger">无效链接: <span id="invalidCount">0</span></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearUris">
                            <i class="fas fa-trash me-1"></i>清空
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="validateUris">
                            <i class="fas fa-check me-1"></i>验证链接
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <button type="button" class="btn btn-primary btn-lg" id="startAnalysis" disabled>
                        <i class="fas fa-play me-2"></i>开始分析
                    </button>
                    <button type="button" class="btn btn-warning btn-lg" id="pauseAnalysis" disabled>
                        <i class="fas fa-pause me-2"></i>暂停
                    </button>
                    <button type="button" class="btn btn-danger btn-lg" id="stopAnalysis" disabled>
                        <i class="fas fa-stop me-2"></i>停止
                    </button>
                </div>
            </div>
        </div>

        <!-- 进度显示区域 -->
        <div class="card mb-4" id="progressSection" style="display: none;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>提取进度
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="text-muted">总体进度</span>
                        <span class="text-muted" id="progressText">0%</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             id="progressFill" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h4 mb-0 text-primary" id="processedCount">0</div>
                            <small class="text-muted">已处理</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h4 mb-0 text-success" id="successCount">0</div>
                            <small class="text-muted">成功</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h4 mb-0 text-danger" id="failedCount">0</div>
                            <small class="text-muted">失败</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border rounded p-2">
                            <div class="h6 mb-0 text-info" id="estimatedTime">--</div>
                            <small class="text-muted">预计完成</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="alert alert-info mb-0" id="currentStatus">
                        <i class="fas fa-info-circle me-2"></i>准备开始...
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速结果预览 -->
        <div class="card" id="quickResultsSection" style="display: none;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>结果预览
                </h5>
                <a href="{{ url_for('results') }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-external-link-alt me-1"></i>查看全部
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>全名</th>
                                <th>粉丝数</th>
                                <th>认证</th>
                                <th>评论</th>
                            </tr>
                        </thead>
                        <tbody id="quickResultsTableBody">
                            <!-- 动态生成的预览数据 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧信息面板 -->
    <div class="col-lg-4">
        <!-- 系统状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>系统状态
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="text-success">
                            <i class="fas fa-database fa-2x"></i>
                            <div class="mt-2">
                                <div class="h5 mb-0" id="totalUsers">0</div>
                                <small>总用户数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-info">
                            <i class="fas fa-comments fa-2x"></i>
                            <div class="mt-2">
                                <div class="h5 mb-0" id="totalComments">0</div>
                                <small>总评论数</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>最近活动
                </h6>
            </div>
            <div class="card-body">
                <div id="recentActivity">
                    <div class="text-muted text-center py-3">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <div>暂无活动记录</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="loadSampleData">
                        <i class="fas fa-download me-1"></i>加载示例数据
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="exportQuick">
                        <i class="fas fa-file-export me-1"></i>快速导出
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" id="clearAllData">
                        <i class="fas fa-trash-alt me-1"></i>清空所有数据
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" id="viewLogs">
                        <i class="fas fa-file-alt me-1"></i>查看日志
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/index.js') }}"></script>
{% endblock %}
