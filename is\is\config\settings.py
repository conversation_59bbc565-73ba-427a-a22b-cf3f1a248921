"""
配置管理模块
负责加载和管理应用配置
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path

from src.core.interfaces import IConfigManager
from src.core.exceptions import ConfigurationError


class ConfigManager(IConfigManager):
    """配置管理器实现类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self._config = {}
        self._config_path = config_path or "config/config.yaml"
        self.load_config(self._config_path)
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict: 配置字典
            
        Raises:
            ConfigurationError: 配置文件加载失败
        """
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                raise ConfigurationError(
                    f"配置文件不存在: {config_path}",
                    error_code="CONFIG_FILE_NOT_FOUND"
                )
            
            with open(config_file, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
            
            # 处理环境变量替换
            self._process_environment_variables()
            
            return self._config
            
        except yaml.YAMLError as e:
            raise ConfigurationError(
                f"配置文件格式错误: {e}",
                error_code="CONFIG_FORMAT_ERROR",
                details={"file_path": config_path, "yaml_error": str(e)}
            )
        except Exception as e:
            raise ConfigurationError(
                f"加载配置文件失败: {e}",
                error_code="CONFIG_LOAD_ERROR",
                details={"file_path": config_path, "error": str(e)}
            )
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点分隔的嵌套键
        
        Args:
            key: 配置键（如 'app.name' 或 'database.path'）
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception:
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 是否设置成功
        """
        try:
            keys = key.split('.')
            config = self._config
            
            # 导航到最后一级的父级
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            config[keys[-1]] = value
            return True
            
        except Exception:
            return False
    
    def save_config(self, config_path: str = None) -> bool:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            path = config_path or self._config_path
            
            # 确保目录存在
            Path(path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            return True
            
        except Exception:
            return False
    
    def _process_environment_variables(self):
        """处理配置中的环境变量替换"""
        def replace_env_vars(obj):
            if isinstance(obj, dict):
                return {k: replace_env_vars(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [replace_env_vars(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith('${') and obj.endswith('}'):
                # 环境变量格式: ${VAR_NAME:default_value}
                env_expr = obj[2:-1]  # 移除 ${ 和 }
                if ':' in env_expr:
                    var_name, default_value = env_expr.split(':', 1)
                    return os.getenv(var_name, default_value)
                else:
                    return os.getenv(env_expr, obj)
            else:
                return obj
        
        self._config = replace_env_vars(self._config)
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get('database', {})
    
    def get_instagram_config(self) -> Dict[str, Any]:
        """获取Instagram API配置"""
        return self.get('instagram', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        return self.get('app', {})
    
    def get_export_config(self) -> Dict[str, Any]:
        """获取导出配置"""
        return self.get('export', {})
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return self.get('security', {})
    
    def get_proxy_config(self) -> Dict[str, Any]:
        """获取代理配置"""
        return self.get('proxy', {})
    
    def is_debug_mode(self) -> bool:
        """是否为调试模式"""
        return self.get('app.debug', False)
    
    def get_batch_size(self) -> int:
        """获取批处理大小"""
        return self.get('instagram.batch_size', 10)
    
    def get_delay_range(self) -> list:
        """获取请求延迟范围"""
        return self.get('instagram.delay_range', [1, 3])


# 全局配置管理器实例
config_manager = ConfigManager()
