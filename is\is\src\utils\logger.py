"""
日志管理模块
提供统一的日志记录功能，支持文件轮转和结构化日志
"""

import os
import json
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from src.core.interfaces import ILogger
from src.core.constants import LOG_LEVEL_INFO, LOGS_PATH


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录为JSON格式
        
        Args:
            record: 日志记录对象
            
        Returns:
            str: JSON格式的日志字符串
        """
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加额外的上下文信息
        if hasattr(record, 'extra_data'):
            log_data['extra'] = record.extra_data
        
        return json.dumps(log_data, ensure_ascii=False)


class Logger(ILogger):
    """日志记录器实现类"""
    
    def __init__(self, name: str = 'instagram_extractor', 
                 level: str = LOG_LEVEL_INFO,
                 log_file: str = None,
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
            log_file: 日志文件路径
            max_file_size: 最大文件大小（字节）
            backup_count: 备份文件数量
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers(log_file, max_file_size, backup_count)
    
    def _setup_handlers(self, log_file: str, max_file_size: int, backup_count: int):
        """
        设置日志处理器
        
        Args:
            log_file: 日志文件路径
            max_file_size: 最大文件大小
            backup_count: 备份文件数量
        """
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            # 确保日志目录存在
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 使用轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(StructuredFormatter())
            self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """记录调试信息"""
        self._log(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录信息"""
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告"""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误"""
        self._log(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误"""
        self._log(logging.CRITICAL, message, **kwargs)
    
    def _log(self, level: int, message: str, **kwargs):
        """
        内部日志记录方法
        
        Args:
            level: 日志级别
            message: 日志消息
            **kwargs: 额外的上下文信息
        """
        # 过滤敏感信息
        filtered_kwargs = self._filter_sensitive_data(kwargs)
        
        # 创建日志记录
        extra = {'extra_data': filtered_kwargs} if filtered_kwargs else {}
        self.logger.log(level, message, extra=extra)
    
    def _filter_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤敏感数据
        
        Args:
            data: 原始数据字典
            
        Returns:
            Dict: 过滤后的数据字典
        """
        sensitive_keys = ['session_id', 'sessionid', 'password', 'token', 'secret']
        filtered_data = {}
        
        for key, value in data.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                # 对敏感信息进行脱敏处理
                if isinstance(value, str) and len(value) > 8:
                    filtered_data[key] = value[:4] + '*' * (len(value) - 8) + value[-4:]
                else:
                    filtered_data[key] = '***'
            else:
                filtered_data[key] = value
        
        return filtered_data
    
    def log_function_call(self, func_name: str, args: tuple = None, 
                         kwargs: dict = None, result: Any = None, 
                         execution_time: float = None):
        """
        记录函数调用信息
        
        Args:
            func_name: 函数名称
            args: 位置参数
            kwargs: 关键字参数
            result: 函数返回结果
            execution_time: 执行时间（秒）
        """
        log_data = {
            'function': func_name,
            'execution_time': execution_time
        }
        
        if args:
            log_data['args_count'] = len(args)
        
        if kwargs:
            log_data['kwargs'] = self._filter_sensitive_data(kwargs)
        
        if result is not None:
            log_data['result_type'] = type(result).__name__
            if hasattr(result, '__len__'):
                try:
                    log_data['result_length'] = len(result)
                except:
                    pass
        
        self.debug(f"函数调用: {func_name}", **log_data)
    
    def log_api_request(self, method: str, url: str, status_code: int = None,
                       response_time: float = None, error: str = None):
        """
        记录API请求信息
        
        Args:
            method: HTTP方法
            url: 请求URL
            status_code: 响应状态码
            response_time: 响应时间（秒）
            error: 错误信息
        """
        log_data = {
            'method': method,
            'url': self._sanitize_url(url),
            'status_code': status_code,
            'response_time': response_time
        }
        
        if error:
            log_data['error'] = error
            self.error(f"API请求失败: {method} {url}", **log_data)
        else:
            self.info(f"API请求: {method} {url}", **log_data)
    
    def _sanitize_url(self, url: str) -> str:
        """
        清理URL中的敏感信息
        
        Args:
            url: 原始URL
            
        Returns:
            str: 清理后的URL
        """
        # 移除查询参数中的敏感信息
        if '?' in url:
            base_url, query = url.split('?', 1)
            # 简单处理，移除所有查询参数
            return base_url + '?...'
        return url
    
    def log_extraction_progress(self, total: int, processed: int, 
                              successful: int, failed: int, 
                              current_item: str = None):
        """
        记录数据提取进度
        
        Args:
            total: 总数量
            processed: 已处理数量
            successful: 成功数量
            failed: 失败数量
            current_item: 当前处理项目
        """
        progress_data = {
            'total': total,
            'processed': processed,
            'successful': successful,
            'failed': failed,
            'progress_percentage': round((processed / total) * 100, 2) if total > 0 else 0
        }
        
        if current_item:
            progress_data['current_item'] = current_item
        
        self.info(f"提取进度: {processed}/{total}", **progress_data)


# 创建全局日志记录器实例
def get_logger(name: str = None, config: Dict[str, Any] = None) -> Logger:
    """
    获取日志记录器实例
    
    Args:
        name: 日志记录器名称
        config: 配置字典
        
    Returns:
        Logger: 日志记录器实例
    """
    if config is None:
        config = {}
    
    logger_name = name or 'instagram_extractor'
    log_file = config.get('file_path', os.path.join(LOGS_PATH, 'app.log'))
    level = config.get('level', LOG_LEVEL_INFO)
    max_file_size = config.get('max_file_size', 10 * 1024 * 1024)
    backup_count = config.get('backup_count', 5)
    
    return Logger(
        name=logger_name,
        level=level,
        log_file=log_file,
        max_file_size=max_file_size,
        backup_count=backup_count
    )


# 全局日志记录器实例
logger = get_logger()
