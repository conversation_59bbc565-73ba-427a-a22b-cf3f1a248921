/**
 * 首页JavaScript功能
 * 处理Session ID验证、URI处理、数据提取等功能
 */

class IndexPageManager {
    constructor() {
        this.sessionId = '';
        this.isConnected = false;
        this.uriList = [];
        this.isExtracting = false;
        this.extractionProgress = {
            total: 0,
            processed: 0,
            successful: 0,
            failed: 0
        };
        
        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindEvents();
        this.loadStoredData();
        this.updateUI();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // Session ID相关事件
        document.getElementById('sessionId').addEventListener('input', 
            Utils.debounce(() => this.onSessionIdChange(), 500));
        document.getElementById('testConnection').addEventListener('click', 
            () => this.testConnection());

        // URI相关事件
        document.getElementById('uriInput').addEventListener('input', 
            Utils.debounce(() => this.onUriInputChange(), 300));
        document.getElementById('validateUris').addEventListener('click', 
            () => this.validateUris());
        document.getElementById('clearUris').addEventListener('click', 
            () => this.clearUris());

        // 操作按钮事件
        document.getElementById('startAnalysis').addEventListener('click', 
            () => this.startAnalysis());
        document.getElementById('pauseAnalysis').addEventListener('click', 
            () => this.pauseAnalysis());
        document.getElementById('stopAnalysis').addEventListener('click', 
            () => this.stopAnalysis());

        // 快速操作事件
        document.getElementById('loadSampleData').addEventListener('click', 
            () => this.loadSampleData());
        document.getElementById('exportQuick').addEventListener('click', 
            () => this.exportQuick());
        document.getElementById('clearAllData').addEventListener('click', 
            () => this.clearAllData());
        document.getElementById('viewLogs').addEventListener('click', 
            () => this.viewLogs());
    }

    /**
     * 加载存储的数据
     */
    loadStoredData() {
        // 加载保存的Session ID
        const storedSessionId = StorageManager.get('sessionId', '');
        if (storedSessionId) {
            document.getElementById('sessionId').value = storedSessionId;
            this.sessionId = storedSessionId;
        }

        // 加载统计数据
        this.loadStatistics();
    }

    /**
     * Session ID变化处理
     */
    onSessionIdChange() {
        const sessionIdInput = document.getElementById('sessionId');
        this.sessionId = sessionIdInput.value.trim();
        
        // 保存到本地存储
        if (this.sessionId) {
            StorageManager.set('sessionId', this.sessionId);
        } else {
            StorageManager.remove('sessionId');
        }

        // 重置连接状态
        this.isConnected = false;
        this.updateSessionStatus('未连接', 'secondary');
        this.updateUI();
    }

    /**
     * 测试连接
     */
    async testConnection() {
        if (!this.sessionId) {
            Utils.showMessage('请先输入Session ID', 'warning');
            return;
        }

        const testButton = document.getElementById('testConnection');
        const originalText = testButton.innerHTML;
        
        try {
            // 显示加载状态
            testButton.innerHTML = '<span class="loading-spinner"></span> 测试中...';
            testButton.disabled = true;
            this.updateSessionStatus('正在测试连接...', 'warning');

            // 发送测试请求
            const response = await ApiClient.post('/auth/test', {
                session_id: this.sessionId
            });

            if (response.success) {
                this.isConnected = true;
                this.updateSessionStatus(`已连接 - ${response.user_info.username}`, 'success');
                Utils.showMessage('连接成功！', 'success');
            } else {
                this.isConnected = false;
                this.updateSessionStatus('连接失败', 'danger');
                Utils.showMessage(response.message || '连接失败', 'error');
            }
        } catch (error) {
            this.isConnected = false;
            this.updateSessionStatus('连接错误', 'danger');
            Utils.showMessage(`连接错误: ${error.message}`, 'error');
        } finally {
            // 恢复按钮状态
            testButton.innerHTML = originalText;
            testButton.disabled = false;
            this.updateUI();
        }
    }

    /**
     * 更新Session状态显示
     */
    updateSessionStatus(text, type) {
        const statusElement = document.getElementById('sessionStatus');
        const iconClass = {
            'success': 'fas fa-check-circle text-success',
            'danger': 'fas fa-times-circle text-danger',
            'warning': 'fas fa-exclamation-circle text-warning',
            'secondary': 'fas fa-circle text-secondary'
        }[type] || 'fas fa-circle text-secondary';

        statusElement.className = `alert alert-${type}`;
        statusElement.innerHTML = `<i class="${iconClass} me-2"></i><span class="status-text">${text}</span>`;
    }

    /**
     * URI输入变化处理
     */
    onUriInputChange() {
        const uriInput = document.getElementById('uriInput');
        const text = uriInput.value.trim();
        
        if (!text) {
            this.uriList = [];
            this.updateUriStats();
            return;
        }

        // 解析URI列表
        const uris = text.split(/[\n,;]+/)
            .map(uri => uri.trim())
            .filter(uri => uri.length > 0);

        this.processUriList(uris);
    }

    /**
     * 处理URI列表
     */
    processUriList(uris) {
        const validUris = [];
        const invalidUris = [];

        uris.forEach(uri => {
            if (Utils.validateInstagramURI(uri)) {
                validUris.push(uri);
            } else {
                invalidUris.push(uri);
            }
        });

        this.uriList = validUris;
        this.updateUriStats(validUris.length, invalidUris.length);
        this.updateUI();
    }

    /**
     * 更新URI统计显示
     */
    updateUriStats(validCount = 0, invalidCount = 0) {
        document.getElementById('validCount').textContent = validCount;
        document.getElementById('invalidCount').textContent = invalidCount;
    }

    /**
     * 验证URI
     */
    validateUris() {
        const uriInput = document.getElementById('uriInput');
        const text = uriInput.value.trim();
        
        if (!text) {
            Utils.showMessage('请先输入URI', 'warning');
            return;
        }

        this.onUriInputChange();
        
        if (this.uriList.length > 0) {
            Utils.showMessage(`验证完成：${this.uriList.length} 个有效链接`, 'success');
        } else {
            Utils.showMessage('没有找到有效的Instagram链接', 'warning');
        }
    }

    /**
     * 清空URI
     */
    clearUris() {
        Utils.showConfirm('确认清空', '确定要清空所有输入的链接吗？', () => {
            document.getElementById('uriInput').value = '';
            this.uriList = [];
            this.updateUriStats();
            this.updateUI();
            Utils.showMessage('已清空所有链接', 'info');
        });
    }

    /**
     * 开始分析
     */
    async startAnalysis() {
        if (!this.isConnected) {
            Utils.showMessage('请先测试连接', 'warning');
            return;
        }

        if (this.uriList.length === 0) {
            Utils.showMessage('请先输入有效的Instagram链接', 'warning');
            return;
        }

        try {
            this.isExtracting = true;
            this.showProgressSection();
            this.updateUI();

            // 初始化进度
            this.extractionProgress = {
                total: this.uriList.length,
                processed: 0,
                successful: 0,
                failed: 0
            };

            // 发送开始请求
            const response = await ApiClient.post('/extract/start', {
                session_id: this.sessionId,
                uris: this.uriList
            });

            if (response.success) {
                Utils.showMessage('开始提取数据...', 'info');
                this.startProgressMonitoring();
            } else {
                throw new Error(response.message || '启动失败');
            }
        } catch (error) {
            this.isExtracting = false;
            this.updateUI();
            Utils.showMessage(`启动失败: ${error.message}`, 'error');
        }
    }

    /**
     * 暂停分析
     */
    async pauseAnalysis() {
        try {
            const response = await ApiClient.post('/extract/pause');
            if (response.success) {
                Utils.showMessage('已暂停提取', 'info');
            }
        } catch (error) {
            Utils.showMessage(`暂停失败: ${error.message}`, 'error');
        }
    }

    /**
     * 停止分析
     */
    stopAnalysis() {
        Utils.showConfirm('确认停止', '确定要停止当前的数据提取吗？', async () => {
            try {
                const response = await ApiClient.post('/extract/stop');
                if (response.success) {
                    this.isExtracting = false;
                    this.updateUI();
                    Utils.showMessage('已停止提取', 'info');
                }
            } catch (error) {
                Utils.showMessage(`停止失败: ${error.message}`, 'error');
            }
        });
    }

    /**
     * 显示进度区域
     */
    showProgressSection() {
        const progressSection = document.getElementById('progressSection');
        progressSection.style.display = 'block';
        progressSection.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * 开始进度监控
     */
    startProgressMonitoring() {
        this.progressInterval = setInterval(async () => {
            try {
                const response = await ApiClient.get('/extract/progress');
                if (response.success) {
                    this.updateProgress(response.progress);
                    
                    if (response.progress.status === 'completed' || response.progress.status === 'stopped') {
                        this.onExtractionComplete(response.progress);
                    }
                }
            } catch (error) {
                console.error('获取进度失败:', error);
            }
        }, CONFIG.PROGRESS_UPDATE_INTERVAL);
    }

    /**
     * 更新进度显示
     */
    updateProgress(progress) {
        const { processed, successful, failed, total, current_status, estimated_time } = progress;
        
        // 更新进度条
        const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
        document.getElementById('progressFill').style.width = `${percentage}%`;
        document.getElementById('progressText').textContent = `${percentage}%`;

        // 更新统计数字
        document.getElementById('processedCount').textContent = processed;
        document.getElementById('successCount').textContent = successful;
        document.getElementById('failedCount').textContent = failed;
        document.getElementById('estimatedTime').textContent = estimated_time || '--';

        // 更新当前状态
        document.getElementById('currentStatus').innerHTML = 
            `<i class="fas fa-info-circle me-2"></i>${current_status || '处理中...'}`;
    }

    /**
     * 提取完成处理
     */
    onExtractionComplete(progress) {
        clearInterval(this.progressInterval);
        this.isExtracting = false;
        this.updateUI();

        const { successful, failed } = progress;
        Utils.showMessage(`提取完成！成功: ${successful}, 失败: ${failed}`, 'success');

        // 显示结果预览
        this.loadQuickResults();
        this.loadStatistics();
    }

    /**
     * 加载快速结果预览
     */
    async loadQuickResults() {
        try {
            const response = await ApiClient.get('/data/recent', { limit: 5 });
            if (response.success && response.data.length > 0) {
                this.showQuickResults(response.data);
            }
        } catch (error) {
            console.error('加载结果预览失败:', error);
        }
    }

    /**
     * 显示快速结果
     */
    showQuickResults(data) {
        const quickResultsSection = document.getElementById('quickResultsSection');
        const tableBody = document.getElementById('quickResultsTableBody');
        
        tableBody.innerHTML = '';
        
        data.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${user.username}</strong></td>
                <td>${user.full_name || '--'}</td>
                <td>${Utils.formatNumber(user.follower_count || 0)}</td>
                <td>${user.is_verified ? '<i class="fas fa-check-circle text-primary"></i>' : '--'}</td>
                <td class="text-truncate" style="max-width: 200px;">${user.comment_text || '--'}</td>
            `;
            tableBody.appendChild(row);
        });
        
        quickResultsSection.style.display = 'block';
    }

    /**
     * 加载统计数据
     */
    async loadStatistics() {
        try {
            const response = await ApiClient.get('/data/statistics');
            if (response.success) {
                document.getElementById('totalUsers').textContent = response.stats.total_users || 0;
                document.getElementById('totalComments').textContent = response.stats.total_comments || 0;
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 加载示例数据
     */
    loadSampleData() {
        const sampleUri = 'https://www.instagram.com/p/DMcnC6ZTroi/';
        document.getElementById('uriInput').value = sampleUri;
        this.onUriInputChange();
        Utils.showMessage('已加载示例数据', 'info');
    }

    /**
     * 快速导出
     */
    async exportQuick() {
        try {
            const response = await ApiClient.post('/data/export', {
                format: 'csv',
                limit: 1000
            });
            
            if (response.success) {
                // 创建下载链接
                const link = document.createElement('a');
                link.href = response.download_url;
                link.download = response.filename;
                link.click();
                
                Utils.showMessage('导出成功！', 'success');
            }
        } catch (error) {
            Utils.showMessage(`导出失败: ${error.message}`, 'error');
        }
    }

    /**
     * 清空所有数据
     */
    clearAllData() {
        Utils.showConfirm('确认清空', '确定要清空所有提取的数据吗？此操作不可恢复！', async () => {
            try {
                const response = await ApiClient.post('/data/clear');
                if (response.success) {
                    this.loadStatistics();
                    Utils.showMessage('已清空所有数据', 'info');
                }
            } catch (error) {
                Utils.showMessage(`清空失败: ${error.message}`, 'error');
            }
        });
    }

    /**
     * 查看日志
     */
    viewLogs() {
        window.open('/logs', '_blank');
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        const startButton = document.getElementById('startAnalysis');
        const pauseButton = document.getElementById('pauseAnalysis');
        const stopButton = document.getElementById('stopAnalysis');

        // 更新按钮状态
        startButton.disabled = !this.isConnected || this.uriList.length === 0 || this.isExtracting;
        pauseButton.disabled = !this.isExtracting;
        stopButton.disabled = !this.isExtracting;

        // 更新按钮文本
        if (this.isExtracting) {
            startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提取中...';
        } else {
            startButton.innerHTML = '<i class="fas fa-play me-2"></i>开始分析';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.indexPageManager = new IndexPageManager();
});
