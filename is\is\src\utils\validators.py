"""
数据验证模块
提供各种数据验证功能，确保输入数据的有效性和安全性
"""

import re
import urllib.parse
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from src.core.exceptions import ValidationError
from src.core.constants import (
    INSTAGRAM_POST_URL_PATTERN, 
    INSTAGRAM_REEL_URL_PATTERN,
    USER_FIELDS_REQUIRED,
    USER_FIELDS_ALL,
    SUPPORTED_EXPORT_FORMATS
)


class DataValidator:
    """数据验证器类"""
    
    @staticmethod
    def validate_session_id(session_id: str) -> bool:
        """
        验证Instagram Session ID格式
        
        Args:
            session_id: Session ID字符串
            
        Returns:
            bool: 是否有效
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if not session_id:
            raise ValidationError("Session ID不能为空", error_code="EMPTY_SESSION_ID")
        
        if not isinstance(session_id, str):
            raise ValidationError("Session ID必须是字符串", error_code="INVALID_SESSION_ID_TYPE")
        
        # 移除空白字符
        session_id = session_id.strip()
        
        # 检查长度（Instagram Session ID通常很长）
        if len(session_id) < 20:
            raise ValidationError("Session ID长度不足", error_code="SESSION_ID_TOO_SHORT")
        
        # 检查是否包含URL编码字符（Instagram Session ID通常包含%3A等）
        if '%' not in session_id:
            raise ValidationError("Session ID格式可能不正确", error_code="INVALID_SESSION_ID_FORMAT")
        
        return True
    
    @staticmethod
    def validate_instagram_uri(uri: str) -> bool:
        """
        验证Instagram URI格式
        
        Args:
            uri: Instagram URI字符串
            
        Returns:
            bool: 是否有效
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if not uri:
            raise ValidationError("URI不能为空", error_code="EMPTY_URI")
        
        if not isinstance(uri, str):
            raise ValidationError("URI必须是字符串", error_code="INVALID_URI_TYPE")
        
        # 移除空白字符
        uri = uri.strip()
        
        # 检查是否是有效的URL
        try:
            parsed = urllib.parse.urlparse(uri)
            if not parsed.scheme or not parsed.netloc:
                raise ValidationError("无效的URL格式", error_code="INVALID_URL_FORMAT")
        except Exception:
            raise ValidationError("URL解析失败", error_code="URL_PARSE_ERROR")
        
        # 检查是否匹配Instagram帖子或Reel模式
        post_pattern = re.compile(INSTAGRAM_POST_URL_PATTERN)
        reel_pattern = re.compile(INSTAGRAM_REEL_URL_PATTERN)
        
        if not (post_pattern.match(uri) or reel_pattern.match(uri)):
            raise ValidationError(
                "不是有效的Instagram帖子或Reel链接", 
                error_code="INVALID_INSTAGRAM_URI",
                details={"uri": uri}
            )
        
        return True
    
    @staticmethod
    def validate_uri_list(uri_list: List[str]) -> Tuple[List[str], List[str]]:
        """
        验证URI列表
        
        Args:
            uri_list: URI字符串列表
            
        Returns:
            Tuple[List[str], List[str]]: (有效URI列表, 无效URI列表)
        """
        if not uri_list:
            return [], []
        
        if not isinstance(uri_list, list):
            raise ValidationError("URI列表必须是列表类型", error_code="INVALID_URI_LIST_TYPE")
        
        valid_uris = []
        invalid_uris = []
        
        for uri in uri_list:
            try:
                if DataValidator.validate_instagram_uri(uri):
                    valid_uris.append(uri.strip())
            except ValidationError:
                invalid_uris.append(uri)
        
        return valid_uris, invalid_uris
    
    @staticmethod
    def parse_uri_input(uri_input: str) -> List[str]:
        """
        解析URI输入字符串，支持多种分隔符
        
        Args:
            uri_input: URI输入字符串
            
        Returns:
            List[str]: 解析后的URI列表
        """
        if not uri_input:
            return []
        
        # 支持换行、逗号、分号分隔
        separators = ['\n', '\r\n', ',', ';']
        uris = [uri_input]
        
        for separator in separators:
            new_uris = []
            for uri in uris:
                new_uris.extend(uri.split(separator))
            uris = new_uris
        
        # 清理空白字符并过滤空字符串
        uris = [uri.strip() for uri in uris if uri.strip()]
        
        # 去重
        return list(dict.fromkeys(uris))
    
    @staticmethod
    def validate_user_data(user_data: Dict[str, Any]) -> bool:
        """
        验证用户数据字典
        
        Args:
            user_data: 用户数据字典
            
        Returns:
            bool: 是否有效
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if not user_data:
            raise ValidationError("用户数据不能为空", error_code="EMPTY_USER_DATA")
        
        if not isinstance(user_data, dict):
            raise ValidationError("用户数据必须是字典类型", error_code="INVALID_USER_DATA_TYPE")
        
        # 检查必需字段
        missing_fields = []
        for field in USER_FIELDS_REQUIRED:
            if field not in user_data or user_data[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationError(
                f"缺少必需字段: {', '.join(missing_fields)}",
                error_code="MISSING_REQUIRED_FIELDS",
                details={"missing_fields": missing_fields}
            )
        
        # 验证字段类型
        DataValidator._validate_user_data_types(user_data)
        
        return True
    
    @staticmethod
    def _validate_user_data_types(user_data: Dict[str, Any]):
        """
        验证用户数据字段类型
        
        Args:
            user_data: 用户数据字典
            
        Raises:
            ValidationError: 类型验证失败时抛出
        """
        type_validations = {
            'user_id': int,
            'username': str,
            'full_name': (str, type(None)),
            'is_private': bool,
            'is_verified': bool,
            'follower_count': int,
            'following_count': int,
            'media_count': int,
            'is_business': bool,
            'comment_text': str,
            'post_url': str
        }
        
        for field, expected_type in type_validations.items():
            if field in user_data:
                value = user_data[field]
                if not isinstance(value, expected_type):
                    raise ValidationError(
                        f"字段 {field} 类型错误，期望 {expected_type}，实际 {type(value)}",
                        error_code="INVALID_FIELD_TYPE",
                        details={"field": field, "expected_type": str(expected_type), "actual_type": str(type(value))}
                    )
    
    @staticmethod
    def validate_export_config(export_config: Dict[str, Any]) -> bool:
        """
        验证导出配置
        
        Args:
            export_config: 导出配置字典
            
        Returns:
            bool: 是否有效
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if not export_config:
            raise ValidationError("导出配置不能为空", error_code="EMPTY_EXPORT_CONFIG")
        
        # 验证导出格式
        export_format = export_config.get('format')
        if not export_format:
            raise ValidationError("必须指定导出格式", error_code="MISSING_EXPORT_FORMAT")
        
        if export_format not in SUPPORTED_EXPORT_FORMATS:
            raise ValidationError(
                f"不支持的导出格式: {export_format}",
                error_code="UNSUPPORTED_EXPORT_FORMAT",
                details={"supported_formats": SUPPORTED_EXPORT_FORMATS}
            )
        
        # 验证选择的字段
        selected_fields = export_config.get('selected_fields', [])
        if selected_fields:
            invalid_fields = [field for field in selected_fields if field not in USER_FIELDS_ALL]
            if invalid_fields:
                raise ValidationError(
                    f"无效的字段: {', '.join(invalid_fields)}",
                    error_code="INVALID_EXPORT_FIELDS",
                    details={"invalid_fields": invalid_fields, "valid_fields": USER_FIELDS_ALL}
                )
        
        # 验证记录数量限制
        max_records = export_config.get('max_records')
        if max_records is not None:
            if not isinstance(max_records, int) or max_records <= 0:
                raise ValidationError(
                    "最大记录数必须是正整数",
                    error_code="INVALID_MAX_RECORDS"
                )
        
        return True
    
    @staticmethod
    def validate_filter_conditions(filters: Dict[str, Any]) -> bool:
        """
        验证筛选条件
        
        Args:
            filters: 筛选条件字典
            
        Returns:
            bool: 是否有效
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        if not filters:
            return True
        
        if not isinstance(filters, dict):
            raise ValidationError("筛选条件必须是字典类型", error_code="INVALID_FILTER_TYPE")
        
        # 验证日期范围
        if 'date_from' in filters or 'date_to' in filters:
            DataValidator._validate_date_range(filters)
        
        # 验证布尔字段
        boolean_fields = ['is_verified', 'is_private', 'is_business']
        for field in boolean_fields:
            if field in filters:
                value = filters[field]
                if not isinstance(value, bool):
                    raise ValidationError(
                        f"字段 {field} 必须是布尔值",
                        error_code="INVALID_BOOLEAN_FILTER"
                    )
        
        # 验证数值范围
        numeric_fields = ['follower_count', 'following_count', 'media_count']
        for field in numeric_fields:
            min_key = f"{field}_min"
            max_key = f"{field}_max"
            if min_key in filters or max_key in filters:
                DataValidator._validate_numeric_range(filters, min_key, max_key)
        
        return True
    
    @staticmethod
    def _validate_date_range(filters: Dict[str, Any]):
        """
        验证日期范围
        
        Args:
            filters: 筛选条件字典
            
        Raises:
            ValidationError: 日期验证失败时抛出
        """
        date_from = filters.get('date_from')
        date_to = filters.get('date_to')
        
        if date_from:
            try:
                if isinstance(date_from, str):
                    datetime.fromisoformat(date_from)
                elif not isinstance(date_from, datetime):
                    raise ValidationError("date_from 必须是日期字符串或datetime对象")
            except ValueError:
                raise ValidationError("date_from 日期格式无效", error_code="INVALID_DATE_FORMAT")
        
        if date_to:
            try:
                if isinstance(date_to, str):
                    datetime.fromisoformat(date_to)
                elif not isinstance(date_to, datetime):
                    raise ValidationError("date_to 必须是日期字符串或datetime对象")
            except ValueError:
                raise ValidationError("date_to 日期格式无效", error_code="INVALID_DATE_FORMAT")
        
        # 检查日期范围逻辑
        if date_from and date_to:
            from_date = datetime.fromisoformat(date_from) if isinstance(date_from, str) else date_from
            to_date = datetime.fromisoformat(date_to) if isinstance(date_to, str) else date_to
            
            if from_date >= to_date:
                raise ValidationError(
                    "开始日期必须早于结束日期",
                    error_code="INVALID_DATE_RANGE"
                )
    
    @staticmethod
    def _validate_numeric_range(filters: Dict[str, Any], min_key: str, max_key: str):
        """
        验证数值范围
        
        Args:
            filters: 筛选条件字典
            min_key: 最小值键名
            max_key: 最大值键名
            
        Raises:
            ValidationError: 数值验证失败时抛出
        """
        min_value = filters.get(min_key)
        max_value = filters.get(max_key)
        
        if min_value is not None:
            if not isinstance(min_value, (int, float)) or min_value < 0:
                raise ValidationError(
                    f"{min_key} 必须是非负数",
                    error_code="INVALID_NUMERIC_FILTER"
                )
        
        if max_value is not None:
            if not isinstance(max_value, (int, float)) or max_value < 0:
                raise ValidationError(
                    f"{max_key} 必须是非负数",
                    error_code="INVALID_NUMERIC_FILTER"
                )
        
        if min_value is not None and max_value is not None:
            if min_value >= max_value:
                raise ValidationError(
                    f"{min_key} 必须小于 {max_key}",
                    error_code="INVALID_NUMERIC_RANGE"
                )
    
    @staticmethod
    def sanitize_string(text: str, max_length: int = None) -> str:
        """
        清理字符串，移除危险字符
        
        Args:
            text: 原始字符串
            max_length: 最大长度限制
            
        Returns:
            str: 清理后的字符串
        """
        if not text:
            return ""
        
        # 移除控制字符
        sanitized = ''.join(char for char in text if ord(char) >= 32 or char in '\t\n\r')
        
        # 限制长度
        if max_length and len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        return sanitized.strip()
    
    @staticmethod
    def extract_media_id(uri: str) -> Optional[str]:
        """
        从Instagram URI中提取媒体ID
        
        Args:
            uri: Instagram URI
            
        Returns:
            Optional[str]: 媒体ID，如果提取失败则返回None
        """
        patterns = [
            re.compile(INSTAGRAM_POST_URL_PATTERN),
            re.compile(INSTAGRAM_REEL_URL_PATTERN)
        ]
        
        for pattern in patterns:
            match = pattern.match(uri)
            if match:
                return match.group(1)
        
        return None
