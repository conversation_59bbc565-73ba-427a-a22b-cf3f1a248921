"""
自定义异常模块
定义系统中使用的所有自定义异常类
"""

from typing import Optional, Dict, Any


class InstagramExtractorError(Exception):
    """
    Instagram提取器基础异常类
    所有自定义异常的基类
    """
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详细信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details
        }


class AuthenticationError(InstagramExtractorError):
    """
    认证相关异常
    当身份验证失败时抛出
    """
    pass


class InvalidSessionError(AuthenticationError):
    """
    无效会话异常
    当session ID无效或过期时抛出
    """
    pass


class ExtractionError(InstagramExtractorError):
    """
    数据提取异常
    当数据提取过程中发生错误时抛出
    """
    pass


class InvalidURIError(ExtractionError):
    """
    无效URI异常
    当提供的Instagram URI格式不正确时抛出
    """
    pass


class RateLimitError(ExtractionError):
    """
    速率限制异常
    当触发Instagram API速率限制时抛出
    """
    pass


class NetworkError(ExtractionError):
    """
    网络错误异常
    当网络请求失败时抛出
    """
    pass


class StorageError(InstagramExtractorError):
    """
    存储相关异常
    当数据存储操作失败时抛出
    """
    pass


class DatabaseError(StorageError):
    """
    数据库错误异常
    当数据库操作失败时抛出
    """
    pass


class ExportError(StorageError):
    """
    导出错误异常
    当数据导出失败时抛出
    """
    pass


class ValidationError(InstagramExtractorError):
    """
    数据验证异常
    当数据验证失败时抛出
    """
    pass


class ConfigurationError(InstagramExtractorError):
    """
    配置错误异常
    当配置文件有误或缺失时抛出
    """
    pass


class UIError(InstagramExtractorError):
    """
    用户界面错误异常
    当UI操作失败时抛出
    """
    pass


class PluginError(InstagramExtractorError):
    """
    插件错误异常
    当插件操作失败时抛出
    """
    pass


# 异常处理工具函数
def handle_instagram_api_error(exc: Exception) -> InstagramExtractorError:
    """
    处理Instagram API异常，转换为自定义异常
    
    Args:
        exc: 原始异常
        
    Returns:
        InstagramExtractorError: 转换后的自定义异常
    """
    error_message = str(exc)
    
    # 根据错误消息判断异常类型
    if "login" in error_message.lower() or "session" in error_message.lower():
        return InvalidSessionError(
            message=f"Session验证失败: {error_message}",
            error_code="INVALID_SESSION",
            details={"original_error": error_message}
        )
    elif "rate" in error_message.lower() or "limit" in error_message.lower():
        return RateLimitError(
            message=f"API速率限制: {error_message}",
            error_code="RATE_LIMITED",
            details={"original_error": error_message}
        )
    elif "network" in error_message.lower() or "connection" in error_message.lower():
        return NetworkError(
            message=f"网络连接错误: {error_message}",
            error_code="NETWORK_ERROR",
            details={"original_error": error_message}
        )
    else:
        return ExtractionError(
            message=f"数据提取失败: {error_message}",
            error_code="EXTRACTION_FAILED",
            details={"original_error": error_message}
        )


def create_error_response(exc: InstagramExtractorError) -> Dict[str, Any]:
    """
    创建标准化的错误响应
    
    Args:
        exc: 自定义异常
        
    Returns:
        Dict: 错误响应字典
    """
    return {
        "success": False,
        "error": exc.to_dict(),
        "timestamp": None  # 将在实际使用时填充
    }
