#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表和初始数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.storage.models import db_manager, User, ExtractionLog
from src.utils.logger import get_logger
from datetime import datetime


def setup_database():
    """初始化数据库"""
    logger = get_logger('setup_database')
    
    try:
        # 确保数据库目录存在
        db_path = Path('data/database')
        db_path.mkdir(parents=True, exist_ok=True)
        
        logger.info("开始初始化数据库...")
        
        # 创建表
        db_manager.create_tables()
        logger.info("数据库表创建成功")
        
        # 检查表是否创建成功
        session = db_manager.get_session()
        try:
            # 测试查询
            user_count = session.query(User).count()
            log_count = session.query(ExtractionLog).count()
            
            logger.info(f"用户表记录数: {user_count}")
            logger.info(f"日志表记录数: {log_count}")
            
            # 获取数据库统计信息
            stats = db_manager.get_database_stats()
            logger.info(f"数据库统计信息: {stats}")
            
        finally:
            db_manager.close_session(session)
        
        logger.info("数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False


def create_sample_data():
    """创建示例数据"""
    logger = get_logger('setup_database')
    
    try:
        session = db_manager.get_session()
        
        # 检查是否已有数据
        existing_count = session.query(User).count()
        if existing_count > 0:
            logger.info(f"数据库中已有 {existing_count} 条记录，跳过示例数据创建")
            return True
        
        logger.info("创建示例数据...")
        
        # 创建示例用户数据
        sample_users = [
            User(
                user_id=1001,
                username='sample_user_1',
                full_name='示例用户1',
                is_verified=True,
                follower_count=10000,
                following_count=500,
                media_count=100,
                biography='这是一个示例用户的个人简介',
                comment_text='这是一条示例评论内容',
                post_url='https://www.instagram.com/p/DMcnC6ZTroi/',
                extraction_time=datetime.now()
            ),
            User(
                user_id=1002,
                username='sample_user_2',
                full_name='示例用户2',
                is_verified=False,
                is_private=True,
                follower_count=500,
                following_count=200,
                media_count=50,
                comment_text='另一条示例评论',
                post_url='https://www.instagram.com/p/DMcnC6ZTroi/',
                extraction_time=datetime.now()
            ),
            User(
                user_id=1003,
                username='sample_user_3',
                full_name='示例用户3',
                is_verified=False,
                is_business=True,
                follower_count=2000,
                following_count=100,
                media_count=200,
                external_url='https://example.com',
                comment_text='商业账户的评论示例',
                post_url='https://www.instagram.com/p/DMcnC6ZTroi/',
                extraction_time=datetime.now()
            )
        ]
        
        # 添加到数据库
        for user in sample_users:
            session.add(user)
        
        session.commit()
        logger.info(f"成功创建 {len(sample_users)} 条示例数据")
        
        return True
        
    except Exception as e:
        logger.error(f"创建示例数据失败: {e}")
        session.rollback()
        return False
    finally:
        db_manager.close_session(session)


def verify_database():
    """验证数据库设置"""
    logger = get_logger('setup_database')
    
    try:
        session = db_manager.get_session()
        
        # 测试基本查询
        user_count = session.query(User).count()
        log_count = session.query(ExtractionLog).count()
        
        logger.info(f"数据库验证成功 - 用户: {user_count}, 日志: {log_count}")
        
        # 测试复杂查询
        verified_users = session.query(User).filter(User.is_verified == True).count()
        private_users = session.query(User).filter(User.is_private == True).count()
        
        logger.info(f"认证用户: {verified_users}, 私人账户: {private_users}")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库验证失败: {e}")
        return False
    finally:
        db_manager.close_session(session)


def main():
    """主函数"""
    print("Instagram评论提取器 - 数据库初始化")
    print("=" * 50)
    
    # 初始化数据库
    print("1. 初始化数据库...")
    if setup_database():
        print("✅ 数据库初始化成功")
    else:
        print("❌ 数据库初始化失败")
        return False
    
    # 创建示例数据
    print("\n2. 创建示例数据...")
    if create_sample_data():
        print("✅ 示例数据创建成功")
    else:
        print("❌ 示例数据创建失败")
    
    # 验证数据库
    print("\n3. 验证数据库...")
    if verify_database():
        print("✅ 数据库验证成功")
    else:
        print("❌ 数据库验证失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 数据库初始化完成！")
    print("\n可以运行以下命令启动应用:")
    print("python main.py")
    
    return True


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
