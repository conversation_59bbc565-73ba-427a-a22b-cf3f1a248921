"""
Instagram客户端模块
使用instagrapi库实现Instagram API调用
"""

import time
import random
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from instagrapi import Client
from instagrapi.exceptions import LoginRequired, ChallengeRequired, RateLimitError

from src.core.interfaces import UserData
from src.core.exceptions import (
    AuthenticationError, InvalidSessionError, RateLimitError as CustomRateLimitError,
    NetworkError, ExtractionError
)
from src.utils.logger import get_logger
from src.utils.validators import DataValidator
from config.settings import config_manager


class InstagramClient:
    """Instagram API客户端"""
    
    def __init__(self):
        """初始化Instagram客户端"""
        self.client = Client()
        self.logger = get_logger('instagram_client')
        self.is_authenticated = False
        self.current_user = None
        
        # 获取配置
        self.config = config_manager.get_instagram_config()
        self.delay_range = self.config.get('delay_range', [1, 3])
        self.max_retries = self.config.get('max_retries', 3)
        self.timeout = self.config.get('timeout', 30)
        
        # 设置客户端配置
        self._configure_client()
    
    def _configure_client(self):
        """配置Instagram客户端"""
        try:
            # 设置延迟范围
            self.client.delay_range = self.delay_range
            
            # 设置用户代理
            user_agent = self.config.get('user_agent')
            if user_agent:
                self.client.set_user_agent(user_agent)
            
            # 设置超时
            self.client.request_timeout = self.timeout
            
            self.logger.info("Instagram客户端配置完成", 
                           delay_range=self.delay_range,
                           timeout=self.timeout)
            
        except Exception as e:
            self.logger.error(f"配置Instagram客户端失败: {e}")
    
    def authenticate(self, session_id: str) -> bool:
        """
        使用Session ID进行身份验证
        
        Args:
            session_id: Instagram Session ID
            
        Returns:
            bool: 是否认证成功
            
        Raises:
            AuthenticationError: 认证失败时抛出
        """
        try:
            # 验证Session ID格式
            DataValidator.validate_session_id(session_id)
            
            self.logger.info("开始Session ID认证")
            
            # 使用Session ID登录
            self.client.login_by_sessionid(session_id)
            
            # 获取当前用户信息验证登录状态
            user_info = self.client.account_info()
            
            self.current_user = {
                'user_id': user_info.pk,
                'username': user_info.username,
                'full_name': user_info.full_name,
                'is_verified': user_info.is_verified,
                'is_private': user_info.is_private
            }
            
            self.is_authenticated = True
            
            self.logger.info("Session ID认证成功", 
                           username=self.current_user['username'],
                           user_id=self.current_user['user_id'])
            
            return True
            
        except LoginRequired as e:
            self.is_authenticated = False
            error_msg = "Session ID无效或已过期"
            self.logger.error(error_msg, error=str(e))
            raise InvalidSessionError(error_msg, error_code="INVALID_SESSION")
            
        except ChallengeRequired as e:
            self.is_authenticated = False
            error_msg = "账户需要验证挑战"
            self.logger.error(error_msg, error=str(e))
            raise AuthenticationError(error_msg, error_code="CHALLENGE_REQUIRED")
            
        except Exception as e:
            self.is_authenticated = False
            error_msg = f"认证失败: {str(e)}"
            self.logger.error(error_msg, error=str(e))
            raise AuthenticationError(error_msg, error_code="AUTH_FAILED")
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """
        获取当前登录用户信息
        
        Returns:
            Optional[Dict]: 用户信息字典
        """
        return self.current_user if self.is_authenticated else None
    
    def extract_media_comments(self, media_url: str) -> List[UserData]:
        """
        提取指定媒体的评论和用户信息
        
        Args:
            media_url: Instagram媒体URL
            
        Returns:
            List[UserData]: 用户数据列表
            
        Raises:
            ExtractionError: 提取失败时抛出
        """
        if not self.is_authenticated:
            raise AuthenticationError("未认证，请先登录", error_code="NOT_AUTHENTICATED")
        
        try:
            self.logger.info(f"开始提取媒体评论", media_url=media_url)
            
            # 从URL获取媒体ID
            media_pk = self.client.media_pk_from_url(media_url)
            self.logger.info(f"获取媒体ID成功", media_pk=media_pk)
            
            # 获取所有评论
            comments = self.client.media_comments(media_pk, amount=0)
            self.logger.info(f"获取评论成功", comment_count=len(comments))
            
            user_data_list = []
            processed_users = set()  # 避免重复处理同一用户

            for i, comment in enumerate(comments):
                try:
                    self.logger.info(f"处理评论 {i+1}/{len(comments)}",
                                   comment_user=comment.user.username)

                    # 添加延迟避免速率限制
                    self._add_delay()

                    user_pk = comment.user.pk

                    # 跳过已处理的用户（但为每个评论创建记录）
                    # if user_pk in processed_users:
                    #     continue

                    # processed_users.add(user_pk)

                    # 只保存基本用户信息和私信所需参数
                    # 注意：comment.user是UserShort类型，某些字段可能不存在
                    user_data = UserData(
                        user_id=comment.user.pk,  # Instagram API私信需要的用户ID
                        username=comment.user.username,  # 用户名
                        full_name=comment.user.full_name or "",  # 全名（现在可以正确获取）
                        profile_pic_url=str(comment.user.profile_pic_url) if comment.user.profile_pic_url else None,  # 头像
                        is_private=comment.user.is_private or False,  # 是否私人账户（影响私信发送）
                        is_verified=getattr(comment.user, 'is_verified', False),  # UserShort可能没有此字段
                        # 评论相关信息
                        comment_text=comment.text,
                        comment_time=comment.created_at_utc,
                        post_url=media_url,
                        extraction_time=datetime.now(),
                        # 不获取详细信息，避免触发反爬虫
                        follower_count=0,  # 设为0，不获取
                        following_count=0,  # 设为0，不获取
                        media_count=0,  # 设为0，不获取
                        biography="",  # 不获取
                        external_url="",  # 不获取
                        is_business=False  # 不获取
                    )

                    user_data_list.append(user_data)

                    self.logger.info(f"用户信息保存完成",
                                   username=user_data.username,
                                   user_id=user_data.user_id,
                                   is_private=user_data.is_private,
                                   comment_preview=comment.text[:30] + "..." if len(comment.text) > 30 else comment.text)

                except Exception as e:
                    self.logger.error(f"处理评论失败",
                                    comment_index=i,
                                    user_id=getattr(comment.user, 'pk', 'unknown'),
                                    error=str(e))

                    # 尝试保存最基本的信息
                    try:
                        user_data = UserData(
                            user_id=comment.user.pk,
                            username=comment.user.username or f"user_{comment.user.pk}",
                            full_name=getattr(comment.user, 'full_name', '') or "",  # 尝试获取full_name
                            comment_text=comment.text,
                            comment_time=comment.created_at_utc,
                            post_url=media_url,
                            extraction_time=datetime.now(),
                            # 其他字段设为默认值
                            follower_count=0,
                            following_count=0,
                            media_count=0,
                            biography="",
                            external_url="",
                            is_business=False,
                            is_private=getattr(comment.user, 'is_private', False),
                            is_verified=getattr(comment.user, 'is_verified', False)
                        )
                        user_data_list.append(user_data)
                        self.logger.info(f"保存最基本信息成功", user_id=comment.user.pk)
                    except Exception as e2:
                        self.logger.error(f"保存最基本信息也失败", error=str(e2))
                        continue
            
            self.logger.info(f"媒体评论提取完成", 
                           total_comments=len(comments),
                           extracted_users=len(user_data_list))
            
            return user_data_list
            
        except RateLimitError as e:
            error_msg = "触发Instagram速率限制"
            self.logger.error(error_msg, error=str(e))
            raise CustomRateLimitError(error_msg, error_code="RATE_LIMITED")
            
        except Exception as e:
            error_msg = f"提取媒体评论失败: {str(e)}"
            self.logger.error(error_msg, media_url=media_url, error=str(e))
            raise ExtractionError(error_msg, error_code="EXTRACTION_FAILED")
    

    
    def _add_delay(self):
        """添加随机延迟（进一步减少延迟时间）"""
        # 由于只获取基本信息，可以进一步减少延迟时间
        delay = random.uniform(0.2, 0.5)  # 进一步减少到0.2-0.5秒
        time.sleep(delay)
    
    def extract_multiple_media(self, media_urls: List[str], 
                             progress_callback: callable = None) -> Tuple[List[UserData], Dict[str, Any]]:
        """
        批量提取多个媒体的评论
        
        Args:
            media_urls: 媒体URL列表
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[List[UserData], Dict]: (用户数据列表, 统计信息)
        """
        if not self.is_authenticated:
            raise AuthenticationError("未认证，请先登录", error_code="NOT_AUTHENTICATED")
        
        all_user_data = []
        stats = {
            'total_urls': len(media_urls),
            'processed_urls': 0,
            'successful_urls': 0,
            'failed_urls': 0,
            'total_users': 0,
            'start_time': datetime.now(),
            'errors': []
        }
        
        self.logger.info(f"开始批量提取", total_urls=len(media_urls))
        
        for i, media_url in enumerate(media_urls):
            try:
                # 更新进度
                if progress_callback:
                    progress_callback(i, len(media_urls), f"正在处理: {media_url}")
                
                # 提取当前媒体的评论
                user_data_list = self.extract_media_comments(media_url)
                all_user_data.extend(user_data_list)
                
                stats['successful_urls'] += 1
                stats['total_users'] += len(user_data_list)
                
                self.logger.info(f"媒体处理成功", 
                               media_url=media_url,
                               user_count=len(user_data_list))
                
            except Exception as e:
                stats['failed_urls'] += 1
                error_info = f"处理失败: {media_url} - {str(e)}"
                stats['errors'].append(error_info)
                
                self.logger.error(f"媒体处理失败", 
                                media_url=media_url, 
                                error=str(e))
            
            finally:
                stats['processed_urls'] += 1
        
        stats['end_time'] = datetime.now()
        stats['duration'] = (stats['end_time'] - stats['start_time']).total_seconds()
        
        self.logger.info(f"批量提取完成", 
                       total_users=stats['total_users'],
                       successful_urls=stats['successful_urls'],
                       failed_urls=stats['failed_urls'],
                       duration=stats['duration'])
        
        return all_user_data, stats


# 全局Instagram客户端实例
instagram_client = InstagramClient()
