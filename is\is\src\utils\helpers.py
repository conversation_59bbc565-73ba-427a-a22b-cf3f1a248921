"""
辅助函数模块
提供各种通用的辅助功能和工具函数
"""

import os
import time
import hashlib
import secrets
import functools
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable, Union
from pathlib import Path

from src.core.constants import MAX_MEMORY_USAGE_MB


def timing_decorator(func: Callable) -> Callable:
    """
    函数执行时间装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        Callable: 装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 记录执行时间（如果有日志记录器）
            try:
                from src.utils.logger import logger
                logger.log_function_call(
                    func_name=func.__name__,
                    args=args,
                    kwargs=kwargs,
                    result=result,
                    execution_time=execution_time
                )
            except ImportError:
                pass
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            # 记录异常
            try:
                from src.utils.logger import logger
                logger.error(f"函数 {func.__name__} 执行失败", 
                           function=func.__name__, 
                           execution_time=execution_time,
                           error=str(e))
            except ImportError:
                pass
            raise
    
    return wrapper


def retry_decorator(max_retries: int = 3, delay: float = 1.0, 
                   backoff: float = 2.0, exceptions: tuple = (Exception,)):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟倍数
        exceptions: 需要重试的异常类型
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries:
                        break
                    
                    # 记录重试信息
                    try:
                        from src.utils.logger import logger
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，{current_delay}秒后重试",
                                     function=func.__name__, 
                                     attempt=attempt + 1,
                                     error=str(e))
                    except ImportError:
                        pass
                    
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            # 所有重试都失败，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator


class FileHelper:
    """文件操作辅助类"""
    
    @staticmethod
    def ensure_directory(path: Union[str, Path]) -> Path:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            path: 目录路径
            
        Returns:
            Path: 目录路径对象
        """
        dir_path = Path(path)
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """
        获取文件大小（字节）
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 文件大小
        """
        try:
            return Path(file_path).stat().st_size
        except (OSError, FileNotFoundError):
            return 0
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            str: 格式化后的大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        # 移除或替换非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            filename = filename.replace(char, '_')
        
        # 移除前后空格和点
        filename = filename.strip(' .')
        
        # 确保文件名不为空
        if not filename:
            filename = "unnamed"
        
        return filename
    
    @staticmethod
    def generate_unique_filename(base_name: str, extension: str, 
                               directory: Union[str, Path]) -> str:
        """
        生成唯一的文件名
        
        Args:
            base_name: 基础文件名
            extension: 文件扩展名
            directory: 目录路径
            
        Returns:
            str: 唯一的文件名
        """
        directory = Path(directory)
        base_name = FileHelper.clean_filename(base_name)
        
        # 确保扩展名以点开头
        if not extension.startswith('.'):
            extension = '.' + extension
        
        filename = f"{base_name}{extension}"
        file_path = directory / filename
        
        # 如果文件不存在，直接返回
        if not file_path.exists():
            return filename
        
        # 文件存在，添加数字后缀
        counter = 1
        while True:
            filename = f"{base_name}_{counter}{extension}"
            file_path = directory / filename
            if not file_path.exists():
                return filename
            counter += 1


class DateTimeHelper:
    """日期时间辅助类"""
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """
        格式化日期时间
        
        Args:
            dt: 日期时间对象
            format_str: 格式字符串
            
        Returns:
            str: 格式化后的字符串
        """
        if not dt:
            return ""
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
        """
        解析日期时间字符串
        
        Args:
            dt_str: 日期时间字符串
            format_str: 格式字符串
            
        Returns:
            Optional[datetime]: 解析后的日期时间对象
        """
        try:
            return datetime.strptime(dt_str, format_str)
        except (ValueError, TypeError):
            return None
    
    @staticmethod
    def get_relative_time(dt: datetime) -> str:
        """
        获取相对时间描述
        
        Args:
            dt: 日期时间对象
            
        Returns:
            str: 相对时间描述
        """
        if not dt:
            return "未知时间"
        
        now = datetime.now()
        if dt.tzinfo:
            # 如果输入时间有时区信息，转换为本地时间
            now = now.replace(tzinfo=dt.tzinfo)
        
        diff = now - dt
        
        if diff.total_seconds() < 60:
            return "刚刚"
        elif diff.total_seconds() < 3600:
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}分钟前"
        elif diff.total_seconds() < 86400:
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}小时前"
        elif diff.days < 30:
            return f"{diff.days}天前"
        elif diff.days < 365:
            months = diff.days // 30
            return f"{months}个月前"
        else:
            years = diff.days // 365
            return f"{years}年前"
    
    @staticmethod
    def estimate_completion_time(processed: int, total: int, 
                               start_time: datetime) -> Optional[datetime]:
        """
        估算完成时间
        
        Args:
            processed: 已处理数量
            total: 总数量
            start_time: 开始时间
            
        Returns:
            Optional[datetime]: 预计完成时间
        """
        if processed <= 0 or total <= 0 or processed >= total:
            return None
        
        now = datetime.now()
        elapsed = (now - start_time).total_seconds()
        
        if elapsed <= 0:
            return None
        
        # 计算平均处理速度
        rate = processed / elapsed
        remaining = total - processed
        
        # 估算剩余时间
        remaining_seconds = remaining / rate
        completion_time = now + timedelta(seconds=remaining_seconds)
        
        return completion_time


class SecurityHelper:
    """安全辅助类"""
    
    @staticmethod
    def generate_random_string(length: int = 32) -> str:
        """
        生成随机字符串
        
        Args:
            length: 字符串长度
            
        Returns:
            str: 随机字符串
        """
        return secrets.token_urlsafe(length)[:length]
    
    @staticmethod
    def hash_string(text: str, algorithm: str = 'sha256') -> str:
        """
        计算字符串哈希值
        
        Args:
            text: 输入字符串
            algorithm: 哈希算法
            
        Returns:
            str: 哈希值
        """
        hash_obj = hashlib.new(algorithm)
        hash_obj.update(text.encode('utf-8'))
        return hash_obj.hexdigest()
    
    @staticmethod
    def mask_sensitive_data(data: str, visible_chars: int = 4) -> str:
        """
        脱敏敏感数据
        
        Args:
            data: 敏感数据
            visible_chars: 可见字符数量
            
        Returns:
            str: 脱敏后的数据
        """
        if not data or len(data) <= visible_chars * 2:
            return '*' * len(data) if data else ''
        
        return data[:visible_chars] + '*' * (len(data) - visible_chars * 2) + data[-visible_chars:]


class MemoryHelper:
    """内存管理辅助类"""
    
    @staticmethod
    def get_memory_usage() -> float:
        """
        获取当前内存使用量（MB）
        
        Returns:
            float: 内存使用量（MB）
        """
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            # 如果没有psutil，返回0
            return 0.0
    
    @staticmethod
    def check_memory_limit() -> bool:
        """
        检查是否超过内存限制
        
        Returns:
            bool: 是否超过限制
        """
        current_usage = MemoryHelper.get_memory_usage()
        return current_usage > MAX_MEMORY_USAGE_MB
    
    @staticmethod
    def force_garbage_collection():
        """强制垃圾回收"""
        import gc
        gc.collect()


class DataHelper:
    """数据处理辅助类"""
    
    @staticmethod
    def chunk_list(data_list: List[Any], chunk_size: int) -> List[List[Any]]:
        """
        将列表分块
        
        Args:
            data_list: 原始列表
            chunk_size: 块大小
            
        Returns:
            List[List[Any]]: 分块后的列表
        """
        if chunk_size <= 0:
            return [data_list]
        
        return [data_list[i:i + chunk_size] for i in range(0, len(data_list), chunk_size)]
    
    @staticmethod
    def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
        """
        扁平化嵌套字典
        
        Args:
            d: 嵌套字典
            parent_key: 父键名
            sep: 分隔符
            
        Returns:
            Dict[str, Any]: 扁平化后的字典
        """
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(DataHelper.flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    @staticmethod
    def safe_get(data: Dict[str, Any], key_path: str, default: Any = None) -> Any:
        """
        安全获取嵌套字典的值
        
        Args:
            data: 数据字典
            key_path: 键路径（用点分隔）
            default: 默认值
            
        Returns:
            Any: 获取的值或默认值
        """
        try:
            keys = key_path.split('.')
            value = data
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError, AttributeError):
            return default
    
    @staticmethod
    def remove_duplicates(data_list: List[Dict[str, Any]], 
                         key_field: str) -> List[Dict[str, Any]]:
        """
        根据指定字段去重
        
        Args:
            data_list: 数据列表
            key_field: 用于去重的字段名
            
        Returns:
            List[Dict[str, Any]]: 去重后的列表
        """
        seen = set()
        result = []
        
        for item in data_list:
            key_value = item.get(key_field)
            if key_value not in seen:
                seen.add(key_value)
                result.append(item)
        
        return result
